<?xml version="1.0" encoding="UTF-8"?>
<report xmlns="http://www.eclipse.org/birt/2005/design" version="3.2.26" id="1">
    <property name="createdBy">Eclipse BIRT Designer Version 4.17.0.v202409011308</property>
    <property name="units">in</property>
    <property name="iconFile">/templates/blank_report.gif</property>
    <property name="bidiLayoutOrientation">ltr</property>
    <property name="imageDPI">96</property>
    <parameters>
        <scalar-parameter name="TNUM" id="528">
            <text-property name="promptText">Enter TNUM:</text-property>
            <property name="valueType">static</property>
            <property name="isRequired">true</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="concealValue">false</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
    </parameters>
    <data-sources>
        <oda-data-source extensionID="org.eclipse.birt.report.data.oda.jdbc" name="Data Source" id="4">
            <list-property name="privateDriverProperties">
                <ex-property>
                    <name>contentBidiFormatStr</name>
                    <value>ILYNN</value>
                </ex-property>
                <ex-property>
                    <name>disabledContentBidiFormatStr</name>
                </ex-property>
                <ex-property>
                    <name>disabledMetadataBidiFormatStr</name>
                </ex-property>
                <ex-property>
                    <name>metadataBidiFormatStr</name>
                    <value>ILYNN</value>
                </ex-property>
            </list-property>
            <property name="odaDriverClass">oracle.jdbc.OracleDriver</property>
            <property name="odaURL">***************************************</property>
            <property name="odaUser">system</property>
            <encrypted-property name="odaPassword" encryptionID="base64">VWx0cmFvbmUyMDI0</encrypted-property>
        </oda-data-source>
    </data-sources>
    <data-sets>
        <oda-data-set extensionID="org.eclipse.birt.report.data.oda.jdbc.JdbcSelectDataSet" name="Data Set 1" id="327">
            <list-property name="columnHints">
                <structure>
                    <property name="columnName">AMT_PERIOD</property>
                    <property name="alias">AMT_PERIOD</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">AMT_PERIOD</text-property>
                    <text-property name="heading">AMT_PERIOD</text-property>
                </structure>
                <structure>
                    <property name="columnName">AMT_PERIOD_</property>
                    <property name="alias">AMT_PERIOD_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">AMT_PERIOD_</text-property>
                    <text-property name="heading">AMT_PERIOD_</text-property>
                </structure>
                <structure>
                    <property name="columnName">B_S</property>
                    <property name="alias">B_S</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">B_S</text-property>
                    <text-property name="heading">B_S</text-property>
                </structure>
                <structure>
                    <property name="columnName">BRO_DESCR</property>
                    <property name="alias">BRO_DESCR</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">BRO_DESCR</text-property>
                    <text-property name="heading">BRO_DESCR</text-property>
                </structure>
                <structure>
                    <property name="columnName">BROKER</property>
                    <property name="alias">BROKER</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">BROKER</text-property>
                    <text-property name="heading">BROKER</text-property>
                </structure>
                <structure>
                    <property name="columnName">BSWAP</property>
                    <property name="alias">BSWAP</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">BSWAP</text-property>
                    <text-property name="heading">BSWAP</text-property>
                </structure>
                <structure>
                    <property name="columnName">CALCAGENT</property>
                    <property name="alias">CALCAGENT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CALCAGENT</text-property>
                    <text-property name="heading">CALCAGENT</text-property>
                </structure>
                <structure>
                    <property name="columnName">CAPCITYPWR</property>
                    <property name="alias">CAPCITYPWR</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CAPCITYPWR</text-property>
                    <text-property name="heading">CAPCITYPWR</text-property>
                </structure>
                <structure>
                    <property name="columnName">CAPCITYPWR2</property>
                    <property name="alias">CAPCITYPWR2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CAPCITYPWR2</text-property>
                    <text-property name="heading">CAPCITYPWR2</text-property>
                </structure>
                <structure>
                    <property name="columnName">CDY1</property>
                    <property name="alias">CDY1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CDY1</text-property>
                    <text-property name="heading">CDY1</text-property>
                </structure>
                <structure>
                    <property name="columnName">CDY1_ATTR1</property>
                    <property name="alias">CDY1_ATTR1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CDY1_ATTR1</text-property>
                    <text-property name="heading">CDY1_ATTR1</text-property>
                </structure>
                <structure>
                    <property name="columnName">CDY1_POWER_REGION</property>
                    <property name="alias">CDY1_POWER_REGION</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CDY1_POWER_REGION</text-property>
                    <text-property name="heading">CDY1_POWER_REGION</text-property>
                </structure>
                <structure>
                    <property name="columnName">CDY1_RISKDESC</property>
                    <property name="alias">CDY1_RISKDESC</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CDY1_RISKDESC</text-property>
                    <text-property name="heading">CDY1_RISKDESC</text-property>
                </structure>
                <structure>
                    <property name="columnName">CDY2</property>
                    <property name="alias">CDY2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CDY2</text-property>
                    <text-property name="heading">CDY2</text-property>
                </structure>
                <structure>
                    <property name="columnName">CDYDESC1</property>
                    <property name="alias">CDYDESC1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CDYDESC1</text-property>
                    <text-property name="heading">CDYDESC1</text-property>
                </structure>
                <structure>
                    <property name="columnName">CDYDESC1_</property>
                    <property name="alias">CDYDESC1_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CDYDESC1_</text-property>
                    <text-property name="heading">CDYDESC1_</text-property>
                </structure>
                <structure>
                    <property name="columnName">CDYDESC2</property>
                    <property name="alias">CDYDESC2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CDYDESC2</text-property>
                    <text-property name="heading">CDYDESC2</text-property>
                </structure>
                <structure>
                    <property name="columnName">CDYDESC2_</property>
                    <property name="alias">CDYDESC2_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CDYDESC2_</text-property>
                    <text-property name="heading">CDYDESC2_</text-property>
                </structure>
                <structure>
                    <property name="columnName">CONF__TITLE</property>
                    <property name="alias">CONF__TITLE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CONF__TITLE</text-property>
                    <text-property name="heading">CONF__TITLE</text-property>
                </structure>
                <structure>
                    <property name="columnName">CPTY</property>
                    <property name="alias">CPTY</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CPTY</text-property>
                    <text-property name="heading">CPTY</text-property>
                </structure>
                <structure>
                    <property name="columnName">CPTY_ADDR1</property>
                    <property name="alias">CPTY_ADDR1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CPTY_ADDR1</text-property>
                    <text-property name="heading">CPTY_ADDR1</text-property>
                </structure>
                <structure>
                    <property name="columnName">CPTY_ADDR2</property>
                    <property name="alias">CPTY_ADDR2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CPTY_ADDR2</text-property>
                    <text-property name="heading">CPTY_ADDR2</text-property>
                </structure>
                <structure>
                    <property name="columnName">CPTY_ADDR3</property>
                    <property name="alias">CPTY_ADDR3</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CPTY_ADDR3</text-property>
                    <text-property name="heading">CPTY_ADDR3</text-property>
                </structure>
                <structure>
                    <property name="columnName">CPTY_ADDR4</property>
                    <property name="alias">CPTY_ADDR4</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CPTY_ADDR4</text-property>
                    <text-property name="heading">CPTY_ADDR4</text-property>
                </structure>
                <structure>
                    <property name="columnName">CPTY_DESCR</property>
                    <property name="alias">CPTY_DESCR</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CPTY_DESCR</text-property>
                    <text-property name="heading">CPTY_DESCR</text-property>
                </structure>
                <structure>
                    <property name="columnName">CPTY_EMAIL</property>
                    <property name="alias">CPTY_EMAIL</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CPTY_EMAIL</text-property>
                    <text-property name="heading">CPTY_EMAIL</text-property>
                </structure>
                <structure>
                    <property name="columnName">CPTY_FAX</property>
                    <property name="alias">CPTY_FAX</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CPTY_FAX</text-property>
                    <text-property name="heading">CPTY_FAX</text-property>
                </structure>
                <structure>
                    <property name="columnName">CPTY_PHONE</property>
                    <property name="alias">CPTY_PHONE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CPTY_PHONE</text-property>
                    <text-property name="heading">CPTY_PHONE</text-property>
                </structure>
                <structure>
                    <property name="columnName">DELDATEEND_STR</property>
                    <property name="alias">DELDATEEND_STR</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">DELDATEEND_STR</text-property>
                    <text-property name="heading">DELDATEEND_STR</text-property>
                </structure>
                <structure>
                    <property name="columnName">DELDATEEND_STR_</property>
                    <property name="alias">DELDATEEND_STR_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">DELDATEEND_STR_</text-property>
                    <text-property name="heading">DELDATEEND_STR_</text-property>
                </structure>
                <structure>
                    <property name="columnName">DELDATESTART_STR</property>
                    <property name="alias">DELDATESTART_STR</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">DELDATESTART_STR</text-property>
                    <text-property name="heading">DELDATESTART_STR</text-property>
                </structure>
                <structure>
                    <property name="columnName">DELDATESTART_STR_</property>
                    <property name="alias">DELDATESTART_STR_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">DELDATESTART_STR_</text-property>
                    <text-property name="heading">DELDATESTART_STR_</text-property>
                </structure>
                <structure>
                    <property name="columnName">DELDATESTART_STR__</property>
                    <property name="alias">DELDATESTART_STR__</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">DELDATESTART_STR__</text-property>
                    <text-property name="heading">DELDATESTART_STR__</text-property>
                </structure>
                <structure>
                    <property name="columnName">ERCOTDESC</property>
                    <property name="alias">ERCOTDESC</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">ERCOTDESC</text-property>
                    <text-property name="heading">ERCOTDESC</text-property>
                </structure>
                <structure>
                    <property name="columnName">FIRMNESS</property>
                    <property name="alias">FIRMNESS</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">FIRMNESS</text-property>
                    <text-property name="heading">FIRMNESS</text-property>
                </structure>
                <structure>
                    <property name="columnName">FIRMPROPCASE</property>
                    <property name="alias">FIRMPROPCASE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">FIRMPROPCASE</text-property>
                    <text-property name="heading">FIRMPROPCASE</text-property>
                </structure>
                <structure>
                    <property name="columnName">FIX_METHD</property>
                    <property name="alias">FIX_METHD</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">FIX_METHD</text-property>
                    <text-property name="heading">FIX_METHD</text-property>
                </structure>
                <structure>
                    <property name="columnName">FIX_METHD1</property>
                    <property name="alias">FIX_METHD1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">FIX_METHD1</text-property>
                    <text-property name="heading">FIX_METHD1</text-property>
                </structure>
                <structure>
                    <property name="columnName">FIX_METHD2</property>
                    <property name="alias">FIX_METHD2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">FIX_METHD2</text-property>
                    <text-property name="heading">FIX_METHD2</text-property>
                </structure>
                <structure>
                    <property name="columnName">FIXPAYOR</property>
                    <property name="alias">FIXPAYOR</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">FIXPAYOR</text-property>
                    <text-property name="heading">FIXPAYOR</text-property>
                </structure>
                <structure>
                    <property name="columnName">FLOAT_MULT</property>
                    <property name="alias">FLOAT_MULT</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">FLOAT_MULT</text-property>
                    <text-property name="heading">FLOAT_MULT</text-property>
                </structure>
                <structure>
                    <property name="columnName">FLOAT_MULT_</property>
                    <property name="alias">FLOAT_MULT_</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">FLOAT_MULT_</text-property>
                    <text-property name="heading">FLOAT_MULT_</text-property>
                </structure>
                <structure>
                    <property name="columnName">FLOAT_MULT__</property>
                    <property name="alias">FLOAT_MULT__</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">FLOAT_MULT__</text-property>
                    <text-property name="heading">FLOAT_MULT__</text-property>
                </structure>
                <structure>
                    <property name="columnName">FLOATPAYOR</property>
                    <property name="alias">FLOATPAYOR</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">FLOATPAYOR</text-property>
                    <text-property name="heading">FLOATPAYOR</text-property>
                </structure>
                <structure>
                    <property name="columnName">HIDEANNEX</property>
                    <property name="alias">HIDEANNEX</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">HIDEANNEX</text-property>
                    <text-property name="heading">HIDEANNEX</text-property>
                </structure>
                <structure>
                    <property name="columnName">HOURALL</property>
                    <property name="alias">HOURALL</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">HOURALL</text-property>
                    <text-property name="heading">HOURALL</text-property>
                </structure>
                <structure>
                    <property name="columnName">HOUSE_ADDR1</property>
                    <property name="alias">HOUSE_ADDR1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">HOUSE_ADDR1</text-property>
                    <text-property name="heading">HOUSE_ADDR1</text-property>
                </structure>
                <structure>
                    <property name="columnName">HOUSE_ADDR2</property>
                    <property name="alias">HOUSE_ADDR2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">HOUSE_ADDR2</text-property>
                    <text-property name="heading">HOUSE_ADDR2</text-property>
                </structure>
                <structure>
                    <property name="columnName">HOUSE_ADDR4</property>
                    <property name="alias">HOUSE_ADDR4</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">HOUSE_ADDR4</text-property>
                    <text-property name="heading">HOUSE_ADDR4</text-property>
                </structure>
                <structure>
                    <property name="columnName">HOUSE_CONTACT</property>
                    <property name="alias">HOUSE_CONTACT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">HOUSE_CONTACT</text-property>
                    <text-property name="heading">HOUSE_CONTACT</text-property>
                </structure>
                <structure>
                    <property name="columnName">HOUSE_ID</property>
                    <property name="alias">HOUSE_ID</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">HOUSE_ID</text-property>
                    <text-property name="heading">HOUSE_ID</text-property>
                </structure>
                <structure>
                    <property name="columnName">HOUSE_TEL</property>
                    <property name="alias">HOUSE_TEL</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">HOUSE_TEL</text-property>
                    <text-property name="heading">HOUSE_TEL</text-property>
                </structure>
                <structure>
                    <property name="columnName">HOUSEFULLNAME</property>
                    <property name="alias">HOUSEFULLNAME</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">HOUSEFULLNAME</text-property>
                    <text-property name="heading">HOUSEFULLNAME</text-property>
                </structure>
                <structure>
                    <property name="columnName">HOUSEFULLNAME_</property>
                    <property name="alias">HOUSEFULLNAME_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">HOUSEFULLNAME_</text-property>
                    <text-property name="heading">HOUSEFULLNAME_</text-property>
                </structure>
                <structure>
                    <property name="columnName">LOGONAME</property>
                    <property name="alias">LOGONAME</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">LOGONAME</text-property>
                    <text-property name="heading">LOGONAME</text-property>
                </structure>
                <structure>
                    <property name="columnName">MA_ATTR1</property>
                    <property name="alias">MA_ATTR1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">MA_ATTR1</text-property>
                    <text-property name="heading">MA_ATTR1</text-property>
                </structure>
                <structure>
                    <property name="columnName">MA_CON_TYPE</property>
                    <property name="alias">MA_CON_TYPE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">MA_CON_TYPE</text-property>
                    <text-property name="heading">MA_CON_TYPE</text-property>
                </structure>
                <structure>
                    <property name="columnName">MA_DESCRIPTION</property>
                    <property name="alias">MA_DESCRIPTION</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">MA_DESCRIPTION</text-property>
                    <text-property name="heading">MA_DESCRIPTION</text-property>
                </structure>
                <structure>
                    <property name="columnName">MA_EFFECTIVE_DT</property>
                    <property name="alias">MA_EFFECTIVE_DT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">MA_EFFECTIVE_DT</text-property>
                    <text-property name="heading">MA_EFFECTIVE_DT</text-property>
                </structure>
                <structure>
                    <property name="columnName">MA_EXPIRATION_DT</property>
                    <property name="alias">MA_EXPIRATION_DT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">MA_EXPIRATION_DT</text-property>
                    <text-property name="heading">MA_EXPIRATION_DT</text-property>
                </structure>
                <structure>
                    <property name="columnName">MANUM</property>
                    <property name="alias">MANUM</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">MANUM</text-property>
                    <text-property name="heading">MANUM</text-property>
                </structure>
                <structure>
                    <property name="columnName">MKT1</property>
                    <property name="alias">MKT1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">MKT1</text-property>
                    <text-property name="heading">MKT1</text-property>
                </structure>
                <structure>
                    <property name="columnName">MKT1_</property>
                    <property name="alias">MKT1_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">MKT1_</text-property>
                    <text-property name="heading">MKT1_</text-property>
                </structure>
                <structure>
                    <property name="columnName">MKT2</property>
                    <property name="alias">MKT2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">MKT2</text-property>
                    <text-property name="heading">MKT2</text-property>
                </structure>
                <structure>
                    <property name="columnName">NUMBLKS</property>
                    <property name="alias">NUMBLKS</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">NUMBLKS</text-property>
                    <text-property name="heading">NUMBLKS</text-property>
                </structure>
                <structure>
                    <property name="columnName">OPT_TYPE</property>
                    <property name="alias">OPT_TYPE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">OPT_TYPE</text-property>
                    <text-property name="heading">OPT_TYPE</text-property>
                </structure>
                <structure>
                    <property name="columnName">OPTMODEL</property>
                    <property name="alias">OPTMODEL</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">OPTMODEL</text-property>
                    <text-property name="heading">OPTMODEL</text-property>
                </structure>
                <structure>
                    <property name="columnName">PHYS_BUYER2</property>
                    <property name="alias">PHYS_BUYER2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PHYS_BUYER2</text-property>
                    <text-property name="heading">PHYS_BUYER2</text-property>
                </structure>
                <structure>
                    <property name="columnName">PHYS_BUYER2_ALT</property>
                    <property name="alias">PHYS_BUYER2_ALT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PHYS_BUYER2_ALT</text-property>
                    <text-property name="heading">PHYS_BUYER2_ALT</text-property>
                </structure>
                <structure>
                    <property name="columnName">PHYS_SELLER2</property>
                    <property name="alias">PHYS_SELLER2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PHYS_SELLER2</text-property>
                    <text-property name="heading">PHYS_SELLER2</text-property>
                </structure>
                <structure>
                    <property name="columnName">PHYS_SELLER2_ALT</property>
                    <property name="alias">PHYS_SELLER2_ALT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PHYS_SELLER2_ALT</text-property>
                    <text-property name="heading">PHYS_SELLER2_ALT</text-property>
                </structure>
                <structure>
                    <property name="columnName">PRC_PERIOD</property>
                    <property name="alias">PRC_PERIOD</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PRC_PERIOD</text-property>
                    <text-property name="heading">PRC_PERIOD</text-property>
                </structure>
                <structure>
                    <property name="columnName">PRC_PERIOD_</property>
                    <property name="alias">PRC_PERIOD_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PRC_PERIOD_</text-property>
                    <text-property name="heading">PRC_PERIOD_</text-property>
                </structure>
                <structure>
                    <property name="columnName">PRC_UNIT</property>
                    <property name="alias">PRC_UNIT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PRC_UNIT</text-property>
                    <text-property name="heading">PRC_UNIT</text-property>
                </structure>
                <structure>
                    <property name="columnName">PRC_UNIT_</property>
                    <property name="alias">PRC_UNIT_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PRC_UNIT_</text-property>
                    <text-property name="heading">PRC_UNIT_</text-property>
                </structure>
                <structure>
                    <property name="columnName">PREM_DATE</property>
                    <property name="alias">PREM_DATE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PREM_DATE</text-property>
                    <text-property name="heading">PREM_DATE</text-property>
                </structure>
                <structure>
                    <property name="columnName">PREM_PERIOD</property>
                    <property name="alias">PREM_PERIOD</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PREM_PERIOD</text-property>
                    <text-property name="heading">PREM_PERIOD</text-property>
                </structure>
                <structure>
                    <property name="columnName">PREM_RATE</property>
                    <property name="alias">PREM_RATE</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">PREM_RATE</text-property>
                    <text-property name="heading">PREM_RATE</text-property>
                </structure>
                <structure>
                    <property name="columnName">PREM_UNIT</property>
                    <property name="alias">PREM_UNIT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PREM_UNIT</text-property>
                    <text-property name="heading">PREM_UNIT</text-property>
                </structure>
                <structure>
                    <property name="columnName">PREMIUM</property>
                    <property name="alias">PREMIUM</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">PREMIUM</text-property>
                    <text-property name="heading">PREMIUM</text-property>
                </structure>
                <structure>
                    <property name="columnName">PRICE_STRIKE</property>
                    <property name="alias">PRICE_STRIKE</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">PRICE_STRIKE</text-property>
                    <text-property name="heading">PRICE_STRIKE</text-property>
                </structure>
                <structure>
                    <property name="columnName">PRICE_STRIKE_</property>
                    <property name="alias">PRICE_STRIKE_</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">PRICE_STRIKE_</text-property>
                    <text-property name="heading">PRICE_STRIKE_</text-property>
                </structure>
                <structure>
                    <property name="columnName">PRICE_UOM</property>
                    <property name="alias">PRICE_UOM</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PRICE_UOM</text-property>
                    <text-property name="heading">PRICE_UOM</text-property>
                </structure>
                <structure>
                    <property name="columnName">PSET_DESC1</property>
                    <property name="alias">PSET_DESC1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PSET_DESC1</text-property>
                    <text-property name="heading">PSET_DESC1</text-property>
                </structure>
                <structure>
                    <property name="columnName">PSET1</property>
                    <property name="alias">PSET1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PSET1</text-property>
                    <text-property name="heading">PSET1</text-property>
                </structure>
                <structure>
                    <property name="columnName">PSET1_</property>
                    <property name="alias">PSET1_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PSET1_</text-property>
                    <text-property name="heading">PSET1_</text-property>
                </structure>
                <structure>
                    <property name="columnName">PSET2</property>
                    <property name="alias">PSET2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PSET2</text-property>
                    <text-property name="heading">PSET2</text-property>
                </structure>
                <structure>
                    <property name="columnName">PUTCALL</property>
                    <property name="alias">PUTCALL</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PUTCALL</text-property>
                    <text-property name="heading">PUTCALL</text-property>
                </structure>
                <structure>
                    <property name="columnName">REPORT_DATE</property>
                    <property name="alias">REPORT_DATE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">REPORT_DATE</text-property>
                    <text-property name="heading">REPORT_DATE</text-property>
                </structure>
                <structure>
                    <property name="columnName">SHOWFLOATING</property>
                    <property name="alias">SHOWFLOATING</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SHOWFLOATING</text-property>
                    <text-property name="heading">SHOWFLOATING</text-property>
                </structure>
                <structure>
                    <property name="columnName">SHOWSCHEDULE</property>
                    <property name="alias">SHOWSCHEDULE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SHOWSCHEDULE</text-property>
                    <text-property name="heading">SHOWSCHEDULE</text-property>
                </structure>
                <structure>
                    <property name="columnName">SHOWSTRIKEUNIT</property>
                    <property name="alias">SHOWSTRIKEUNIT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SHOWSTRIKEUNIT</text-property>
                    <text-property name="heading">SHOWSTRIKEUNIT</text-property>
                </structure>
                <structure>
                    <property name="columnName">SIDE1</property>
                    <property name="alias">SIDE1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SIDE1</text-property>
                    <text-property name="heading">SIDE1</text-property>
                </structure>
                <structure>
                    <property name="columnName">SIDE2</property>
                    <property name="alias">SIDE2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SIDE2</text-property>
                    <text-property name="heading">SIDE2</text-property>
                </structure>
                <structure>
                    <property name="columnName">STRIKE</property>
                    <property name="alias">STRIKE</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">STRIKE</text-property>
                    <text-property name="heading">STRIKE</text-property>
                </structure>
                <structure>
                    <property name="columnName">STYLE1C</property>
                    <property name="alias">STYLE1C</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">STYLE1C</text-property>
                    <text-property name="heading">STYLE1C</text-property>
                </structure>
                <structure>
                    <property name="columnName">SWAPCLRWCAP</property>
                    <property name="alias">SWAPCLRWCAP</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SWAPCLRWCAP</text-property>
                    <text-property name="heading">SWAPCLRWCAP</text-property>
                </structure>
                <structure>
                    <property name="columnName">TNUM</property>
                    <property name="alias">TNUM</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TNUM</text-property>
                    <text-property name="heading">TNUM</text-property>
                </structure>
                <structure>
                    <property name="columnName">TRADE_DATE</property>
                    <property name="alias">TRADE_DATE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TRADE_DATE</text-property>
                    <text-property name="heading">TRADE_DATE</text-property>
                </structure>
                <structure>
                    <property name="columnName">TRADE_TEMPLATE</property>
                    <property name="alias">TRADE_TEMPLATE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TRADE_TEMPLATE</text-property>
                    <text-property name="heading">TRADE_TEMPLATE</text-property>
                </structure>
                <structure>
                    <property name="columnName">TRADE_TEMPLATE_</property>
                    <property name="alias">TRADE_TEMPLATE_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TRADE_TEMPLATE_</text-property>
                    <text-property name="heading">TRADE_TEMPLATE_</text-property>
                </structure>
                <structure>
                    <property name="columnName">TRADE_TYPE2</property>
                    <property name="alias">TRADE_TYPE2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TRADE_TYPE2</text-property>
                    <text-property name="heading">TRADE_TYPE2</text-property>
                </structure>
                <structure>
                    <property name="columnName">TRADETYPEINDEX</property>
                    <property name="alias">TRADETYPEINDEX</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TRADETYPEINDEX</text-property>
                    <text-property name="heading">TRADETYPEINDEX</text-property>
                </structure>
                <structure>
                    <property name="columnName">UOM</property>
                    <property name="alias">UOM</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">UOM</text-property>
                    <text-property name="heading">UOM</text-property>
                </structure>
                <structure>
                    <property name="columnName">ZKEY</property>
                    <property name="alias">ZKEY</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">ZKEY</text-property>
                    <text-property name="heading">ZKEY</text-property>
                </structure>
                <structure>
                    <property name="columnName">ZKEY_</property>
                    <property name="alias">ZKEY_</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">ZKEY_</text-property>
                    <text-property name="heading">ZKEY_</text-property>
                </structure>
                <structure>
                    <property name="columnName">PREMIUMGRID</property>
                    <property name="alias">PREMIUMGRID</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PREMIUMGRID</text-property>
                    <text-property name="heading">PREMIUMGRID</text-property>
                </structure>
                <structure>
                    <property name="columnName">SHOWDAYCONVENTIONS</property>
                    <property name="alias">SHOWDAYCONVENTIONS</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SHOWDAYCONVENTIONS</text-property>
                    <text-property name="heading">SHOWDAYCONVENTIONS</text-property>
                </structure>
                <structure>
                    <property name="columnName">SHOWBUYERSELLER</property>
                    <property name="alias">SHOWBUYERSELLER</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SHOWBUYERSELLER</text-property>
                    <text-property name="heading">SHOWBUYERSELLER</text-property>
                </structure>
                <structure>
                    <property name="columnName">SHOWFIRMNESS</property>
                    <property name="alias">SHOWFIRMNESS</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SHOWFIRMNESS</text-property>
                    <text-property name="heading">SHOWFIRMNESS</text-property>
                </structure>
                <structure>
                    <property name="columnName">SHOWPUTCALL</property>
                    <property name="alias">SHOWPUTCALL</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SHOWPUTCALL</text-property>
                    <text-property name="heading">SHOWPUTCALL</text-property>
                </structure>
                <structure>
                    <property name="columnName">SHOWCOMMODITY</property>
                    <property name="alias">SHOWCOMMODITY</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SHOWCOMMODITY</text-property>
                    <text-property name="heading">SHOWCOMMODITY</text-property>
                </structure>
                <structure>
                    <property name="columnName">DELIVERYTYPE</property>
                    <property name="alias">DELIVERYTYPE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">DELIVERYTYPE</text-property>
                    <text-property name="heading">DELIVERYTYPE</text-property>
                </structure>
                <structure>
                    <property name="columnName">SHOWCALCAGENT</property>
                    <property name="alias">SHOWCALCAGENT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SHOWCALCAGENT</text-property>
                    <text-property name="heading">SHOWCALCAGENT</text-property>
                </structure>
                <structure>
                    <property name="columnName">SHOWSETTLEMENTPRICE</property>
                    <property name="alias">SHOWSETTLEMENTPRICE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SHOWSETTLEMENTPRICE</text-property>
                    <text-property name="heading">SHOWSETTLEMENTPRICE</text-property>
                </structure>
            </list-property>
            <list-property name="parameters">
                <structure>
                    <property name="name">param_1</property>
                    <property name="paramName">TNUM</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                    <property name="position">1</property>
                    <property name="isOptional">true</property>
                    <property name="allowNull">true</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
            </list-property>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">B_S</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">2</property>
                        <property name="name">CDY1_ATTR1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">3</property>
                        <property name="name">CDYDESC1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">4</property>
                        <property name="name">CDY1_RISKDESC</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">5</property>
                        <property name="name">FIX_METHD1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">6</property>
                        <property name="name">MKT1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">7</property>
                        <property name="name">PSET1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">8</property>
                        <property name="name">AMT_PERIOD</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">9</property>
                        <property name="name">ZKEY</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">10</property>
                        <property name="name">NUMBLKS</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">11</property>
                        <property name="name">BRO_DESCR</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">12</property>
                        <property name="name">BROKER</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">13</property>
                        <property name="name">CDYDESC1_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">14</property>
                        <property name="name">CDYDESC2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">15</property>
                        <property name="name">CDY1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">16</property>
                        <property name="name">CDYDESC2_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">17</property>
                        <property name="name">CDY1_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">18</property>
                        <property name="name">CDY2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">19</property>
                        <property name="name">CPTY</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">20</property>
                        <property name="name">CPTY_ADDR1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">21</property>
                        <property name="name">CPTY_ADDR2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">22</property>
                        <property name="name">CPTY_ADDR3</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">23</property>
                        <property name="name">CPTY_ADDR4</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">24</property>
                        <property name="name">CPTY_DESCR</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">25</property>
                        <property name="name">CPTY_FAX</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">26</property>
                        <property name="name">CPTY_PHONE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">27</property>
                        <property name="name">CPTY_EMAIL</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">28</property>
                        <property name="name">DELDATEEND_STR</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">29</property>
                        <property name="name">DELDATESTART_STR</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">30</property>
                        <property name="name">DELDATEEND_STR_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">31</property>
                        <property name="name">FIRMNESS</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">32</property>
                        <property name="name">FIX_METHD2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">33</property>
                        <property name="name">FIX_METHD</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">34</property>
                        <property name="name">FLOAT_MULT</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">35</property>
                        <property name="name">FLOAT_MULT_</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">36</property>
                        <property name="name">FLOAT_MULT__</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">37</property>
                        <property name="name">HOURALL</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">38</property>
                        <property name="name">HOUSE_ADDR1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">39</property>
                        <property name="name">HOUSE_ADDR2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">40</property>
                        <property name="name">HOUSE_ADDR4</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">41</property>
                        <property name="name">HOUSEFULLNAME</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">42</property>
                        <property name="name">HOUSEFULLNAME_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">43</property>
                        <property name="name">HOUSE_ID</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">44</property>
                        <property name="name">MA_ATTR1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">45</property>
                        <property name="name">MA_CON_TYPE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">46</property>
                        <property name="name">MA_DESCRIPTION</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">47</property>
                        <property name="name">MA_EFFECTIVE_DT</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">48</property>
                        <property name="name">MA_EXPIRATION_DT</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">49</property>
                        <property name="name">MANUM</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">50</property>
                        <property name="name">MKT1_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">51</property>
                        <property name="name">MKT2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">52</property>
                        <property name="name">OPTMODEL</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">53</property>
                        <property name="name">OPT_TYPE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">54</property>
                        <property name="name">PHYS_BUYER2_ALT</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">55</property>
                        <property name="name">PHYS_BUYER2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">56</property>
                        <property name="name">PHYS_SELLER2_ALT</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">57</property>
                        <property name="name">PHYS_SELLER2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">58</property>
                        <property name="name">CDY1_POWER_REGION</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">59</property>
                        <property name="name">PRC_PERIOD</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">60</property>
                        <property name="name">PRC_UNIT</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">61</property>
                        <property name="name">PREM_DATE</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">62</property>
                        <property name="name">PREM_PERIOD</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">63</property>
                        <property name="name">PREM_RATE</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">64</property>
                        <property name="name">PREM_UNIT</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">65</property>
                        <property name="name">PREMIUM</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">66</property>
                        <property name="name">STRIKE</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">67</property>
                        <property name="name">PRC_PERIOD_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">68</property>
                        <property name="name">PRICE_STRIKE</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">69</property>
                        <property name="name">PRC_UNIT_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">70</property>
                        <property name="name">PSET1_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">71</property>
                        <property name="name">PSET2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">72</property>
                        <property name="name">PSET_DESC1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">73</property>
                        <property name="name">PUTCALL</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">74</property>
                        <property name="name">AMT_PERIOD_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">75</property>
                        <property name="name">UOM</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">76</property>
                        <property name="name">REPORT_DATE</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">77</property>
                        <property name="name">DELDATESTART_STR_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">78</property>
                        <property name="name">DELDATESTART_STR__</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">79</property>
                        <property name="name">PRICE_STRIKE_</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">80</property>
                        <property name="name">TNUM</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">81</property>
                        <property name="name">TRADE_DATE</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">82</property>
                        <property name="name">TRADE_TEMPLATE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">83</property>
                        <property name="name">TRADE_TEMPLATE_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">84</property>
                        <property name="name">TRADE_TYPE2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">85</property>
                        <property name="name">ZKEY_</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">86</property>
                        <property name="name">LOGONAME</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">87</property>
                        <property name="name">ERCOTDESC</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">88</property>
                        <property name="name">CALCAGENT</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">89</property>
                        <property name="name">FIXPAYOR</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">90</property>
                        <property name="name">FLOATPAYOR</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">91</property>
                        <property name="name">CAPCITYPWR</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">92</property>
                        <property name="name">CAPCITYPWR2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">93</property>
                        <property name="name">FIRMPROPCASE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">94</property>
                        <property name="name">SIDE1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">95</property>
                        <property name="name">SIDE2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">96</property>
                        <property name="name">TRADETYPEINDEX</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">97</property>
                        <property name="name">HOUSE_CONTACT</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">98</property>
                        <property name="name">CONF__TITLE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">99</property>
                        <property name="name">STYLE1C</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">100</property>
                        <property name="name">SWAPCLRWCAP</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">101</property>
                        <property name="name">HIDEANNEX</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">102</property>
                        <property name="name">BSWAP</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">103</property>
                        <property name="name">HOUSE_TEL</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">104</property>
                        <property name="name">RISKDESC</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">105</property>
                        <property name="name">PRICE_UOM</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">106</property>
                        <property name="name">SHOWSCHEDULE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">107</property>
                        <property name="name">SHOWQTY</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">108</property>
                        <property name="name">SHOWSTRIKEUNIT</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">109</property>
                        <property name="name">SHOWFLOATING</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">110</property>
                        <property name="name">PREMIUMGRID</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">111</property>
                        <property name="name">SHOWDAYCONVENTIONS</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">112</property>
                        <property name="name">SHOWBUYERSELLER</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">113</property>
                        <property name="name">SHOWFIRMNESS</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">114</property>
                        <property name="name">SHOWPUTCALL</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">115</property>
                        <property name="name">SHOWCOMMODITY</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">116</property>
                        <property name="name">DELIVERYTYPE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">117</property>
                        <property name="name">SHOWCALCAGENT</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">118</property>
                        <property name="name">SHOWSETTLEMENTPRICE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">119</property>
                        <property name="name">ASSETTYPE</property>
                        <property name="dataType">string</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">Data Source</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">B_S</property>
                    <property name="nativeName">B_S</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">2</property>
                    <property name="name">CDY1_ATTR1</property>
                    <property name="nativeName">CDY1_ATTR1</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">3</property>
                    <property name="name">CDYDESC1</property>
                    <property name="nativeName">CDYDESC1</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">4</property>
                    <property name="name">CDY1_RISKDESC</property>
                    <property name="nativeName">CDY1_RISKDESC</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">5</property>
                    <property name="name">FIX_METHD1</property>
                    <property name="nativeName">FIX_METHD1</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">6</property>
                    <property name="name">MKT1</property>
                    <property name="nativeName">MKT1</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">7</property>
                    <property name="name">PSET1</property>
                    <property name="nativeName">PSET1</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">8</property>
                    <property name="name">AMT_PERIOD</property>
                    <property name="nativeName">AMT_PERIOD</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">9</property>
                    <property name="name">ZKEY</property>
                    <property name="nativeName">ZKEY</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">10</property>
                    <property name="name">NUMBLKS</property>
                    <property name="nativeName">NUMBLKS</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">11</property>
                    <property name="name">BRO_DESCR</property>
                    <property name="nativeName">BRO_DESCR</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">12</property>
                    <property name="name">BROKER</property>
                    <property name="nativeName">BROKER</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">13</property>
                    <property name="name">CDYDESC1_</property>
                    <property name="nativeName">CDYDESC1_</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">14</property>
                    <property name="name">CDYDESC2</property>
                    <property name="nativeName">CDYDESC2</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">15</property>
                    <property name="name">CDY1</property>
                    <property name="nativeName">CDY1</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">16</property>
                    <property name="name">CDYDESC2_</property>
                    <property name="nativeName">CDYDESC2_</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">17</property>
                    <property name="name">CDY1_</property>
                    <property name="nativeName">CDY1_</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">18</property>
                    <property name="name">CDY2</property>
                    <property name="nativeName">CDY2</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">19</property>
                    <property name="name">CPTY</property>
                    <property name="nativeName">CPTY</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">20</property>
                    <property name="name">CPTY_ADDR1</property>
                    <property name="nativeName">CPTY_ADDR1</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">21</property>
                    <property name="name">CPTY_ADDR2</property>
                    <property name="nativeName">CPTY_ADDR2</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">22</property>
                    <property name="name">CPTY_ADDR3</property>
                    <property name="nativeName">CPTY_ADDR3</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">23</property>
                    <property name="name">CPTY_ADDR4</property>
                    <property name="nativeName">CPTY_ADDR4</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">24</property>
                    <property name="name">CPTY_DESCR</property>
                    <property name="nativeName">CPTY_DESCR</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">25</property>
                    <property name="name">CPTY_FAX</property>
                    <property name="nativeName">CPTY_FAX</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">26</property>
                    <property name="name">CPTY_PHONE</property>
                    <property name="nativeName">CPTY_PHONE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">27</property>
                    <property name="name">CPTY_EMAIL</property>
                    <property name="nativeName">CPTY_EMAIL</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">28</property>
                    <property name="name">DELDATEEND_STR</property>
                    <property name="nativeName">DELDATEEND_STR</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">29</property>
                    <property name="name">DELDATESTART_STR</property>
                    <property name="nativeName">DELDATESTART_STR</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">30</property>
                    <property name="name">DELDATEEND_STR_</property>
                    <property name="nativeName">DELDATEEND_STR_</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">31</property>
                    <property name="name">FIRMNESS</property>
                    <property name="nativeName">FIRMNESS</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">32</property>
                    <property name="name">FIX_METHD2</property>
                    <property name="nativeName">FIX_METHD2</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">33</property>
                    <property name="name">FIX_METHD</property>
                    <property name="nativeName">FIX_METHD</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">34</property>
                    <property name="name">FLOAT_MULT</property>
                    <property name="nativeName">FLOAT_MULT</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">35</property>
                    <property name="name">FLOAT_MULT_</property>
                    <property name="nativeName">FLOAT_MULT_</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">36</property>
                    <property name="name">FLOAT_MULT__</property>
                    <property name="nativeName">FLOAT_MULT__</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">37</property>
                    <property name="name">HOURALL</property>
                    <property name="nativeName">HOURALL</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">38</property>
                    <property name="name">HOUSE_ADDR1</property>
                    <property name="nativeName">HOUSE_ADDR1</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">39</property>
                    <property name="name">HOUSE_ADDR2</property>
                    <property name="nativeName">HOUSE_ADDR2</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">40</property>
                    <property name="name">HOUSE_ADDR4</property>
                    <property name="nativeName">HOUSE_ADDR4</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">41</property>
                    <property name="name">HOUSEFULLNAME</property>
                    <property name="nativeName">HOUSEFULLNAME</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">42</property>
                    <property name="name">HOUSEFULLNAME_</property>
                    <property name="nativeName">HOUSEFULLNAME_</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">43</property>
                    <property name="name">HOUSE_ID</property>
                    <property name="nativeName">HOUSE_ID</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">44</property>
                    <property name="name">MA_ATTR1</property>
                    <property name="nativeName">MA_ATTR1</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">45</property>
                    <property name="name">MA_CON_TYPE</property>
                    <property name="nativeName">MA_CON_TYPE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">46</property>
                    <property name="name">MA_DESCRIPTION</property>
                    <property name="nativeName">MA_DESCRIPTION</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">47</property>
                    <property name="name">MA_EFFECTIVE_DT</property>
                    <property name="nativeName">MA_EFFECTIVE_DT</property>
                    <property name="dataType">date-time</property>
                </structure>
                <structure>
                    <property name="position">48</property>
                    <property name="name">MA_EXPIRATION_DT</property>
                    <property name="nativeName">MA_EXPIRATION_DT</property>
                    <property name="dataType">date-time</property>
                </structure>
                <structure>
                    <property name="position">49</property>
                    <property name="name">MANUM</property>
                    <property name="nativeName">MANUM</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">50</property>
                    <property name="name">MKT1_</property>
                    <property name="nativeName">MKT1_</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">51</property>
                    <property name="name">MKT2</property>
                    <property name="nativeName">MKT2</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">52</property>
                    <property name="name">OPTMODEL</property>
                    <property name="nativeName">OPTMODEL</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">53</property>
                    <property name="name">OPT_TYPE</property>
                    <property name="nativeName">OPT_TYPE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">54</property>
                    <property name="name">PHYS_BUYER2_ALT</property>
                    <property name="nativeName">PHYS_BUYER2_ALT</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">55</property>
                    <property name="name">PHYS_BUYER2</property>
                    <property name="nativeName">PHYS_BUYER2</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">56</property>
                    <property name="name">PHYS_SELLER2_ALT</property>
                    <property name="nativeName">PHYS_SELLER2_ALT</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">57</property>
                    <property name="name">PHYS_SELLER2</property>
                    <property name="nativeName">PHYS_SELLER2</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">58</property>
                    <property name="name">CDY1_POWER_REGION</property>
                    <property name="nativeName">CDY1_POWER_REGION</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">59</property>
                    <property name="name">PRC_PERIOD</property>
                    <property name="nativeName">PRC_PERIOD</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">60</property>
                    <property name="name">PRC_UNIT</property>
                    <property name="nativeName">PRC_UNIT</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">61</property>
                    <property name="name">PREM_DATE</property>
                    <property name="nativeName">PREM_DATE</property>
                    <property name="dataType">date-time</property>
                </structure>
                <structure>
                    <property name="position">62</property>
                    <property name="name">PREM_PERIOD</property>
                    <property name="nativeName">PREM_PERIOD</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">63</property>
                    <property name="name">PREM_RATE</property>
                    <property name="nativeName">PREM_RATE</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">64</property>
                    <property name="name">PREM_UNIT</property>
                    <property name="nativeName">PREM_UNIT</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">65</property>
                    <property name="name">PREMIUM</property>
                    <property name="nativeName">PREMIUM</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">66</property>
                    <property name="name">STRIKE</property>
                    <property name="nativeName">STRIKE</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">67</property>
                    <property name="name">PRC_PERIOD_</property>
                    <property name="nativeName">PRC_PERIOD_</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">68</property>
                    <property name="name">PRICE_STRIKE</property>
                    <property name="nativeName">PRICE_STRIKE</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">69</property>
                    <property name="name">PRC_UNIT_</property>
                    <property name="nativeName">PRC_UNIT_</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">70</property>
                    <property name="name">PSET1_</property>
                    <property name="nativeName">PSET1_</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">71</property>
                    <property name="name">PSET2</property>
                    <property name="nativeName">PSET2</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">72</property>
                    <property name="name">PSET_DESC1</property>
                    <property name="nativeName">PSET_DESC1</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">73</property>
                    <property name="name">PUTCALL</property>
                    <property name="nativeName">PUTCALL</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">74</property>
                    <property name="name">AMT_PERIOD_</property>
                    <property name="nativeName">AMT_PERIOD_</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">75</property>
                    <property name="name">UOM</property>
                    <property name="nativeName">UOM</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">76</property>
                    <property name="name">REPORT_DATE</property>
                    <property name="nativeName">REPORT_DATE</property>
                    <property name="dataType">date-time</property>
                </structure>
                <structure>
                    <property name="position">77</property>
                    <property name="name">DELDATESTART_STR_</property>
                    <property name="nativeName">DELDATESTART_STR_</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">78</property>
                    <property name="name">DELDATESTART_STR__</property>
                    <property name="nativeName">DELDATESTART_STR__</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">79</property>
                    <property name="name">PRICE_STRIKE_</property>
                    <property name="nativeName">PRICE_STRIKE_</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">80</property>
                    <property name="name">TNUM</property>
                    <property name="nativeName">TNUM</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">81</property>
                    <property name="name">TRADE_DATE</property>
                    <property name="nativeName">TRADE_DATE</property>
                    <property name="dataType">date-time</property>
                </structure>
                <structure>
                    <property name="position">82</property>
                    <property name="name">TRADE_TEMPLATE</property>
                    <property name="nativeName">TRADE_TEMPLATE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">83</property>
                    <property name="name">TRADE_TEMPLATE_</property>
                    <property name="nativeName">TRADE_TEMPLATE_</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">84</property>
                    <property name="name">TRADE_TYPE2</property>
                    <property name="nativeName">TRADE_TYPE2</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">85</property>
                    <property name="name">ZKEY_</property>
                    <property name="nativeName">ZKEY_</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">86</property>
                    <property name="name">LOGONAME</property>
                    <property name="nativeName">LOGONAME</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">87</property>
                    <property name="name">ERCOTDESC</property>
                    <property name="nativeName">ERCOTDESC</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">88</property>
                    <property name="name">CALCAGENT</property>
                    <property name="nativeName">CALCAGENT</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">89</property>
                    <property name="name">FIXPAYOR</property>
                    <property name="nativeName">FIXPAYOR</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">90</property>
                    <property name="name">FLOATPAYOR</property>
                    <property name="nativeName">FLOATPAYOR</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">91</property>
                    <property name="name">CAPCITYPWR</property>
                    <property name="nativeName">CAPCITYPWR</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">92</property>
                    <property name="name">CAPCITYPWR2</property>
                    <property name="nativeName">CAPCITYPWR2</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">93</property>
                    <property name="name">FIRMPROPCASE</property>
                    <property name="nativeName">FIRMPROPCASE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">94</property>
                    <property name="name">SIDE1</property>
                    <property name="nativeName">SIDE1</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">95</property>
                    <property name="name">SIDE2</property>
                    <property name="nativeName">SIDE2</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">96</property>
                    <property name="name">TRADETYPEINDEX</property>
                    <property name="nativeName">TRADETYPEINDEX</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">97</property>
                    <property name="name">HOUSE_CONTACT</property>
                    <property name="nativeName">HOUSE_CONTACT</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">98</property>
                    <property name="name">CONF__TITLE</property>
                    <property name="nativeName">CONF__TITLE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">99</property>
                    <property name="name">STYLE1C</property>
                    <property name="nativeName">STYLE1C</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">100</property>
                    <property name="name">SWAPCLRWCAP</property>
                    <property name="nativeName">SWAPCLRWCAP</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">101</property>
                    <property name="name">HIDEANNEX</property>
                    <property name="nativeName">HIDEANNEX</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">102</property>
                    <property name="name">BSWAP</property>
                    <property name="nativeName">BSWAP</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">103</property>
                    <property name="name">HOUSE_TEL</property>
                    <property name="nativeName">HOUSE_TEL</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">104</property>
                    <property name="name">RISKDESC</property>
                    <property name="nativeName">RISKDESC</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">105</property>
                    <property name="name">PRICE_UOM</property>
                    <property name="nativeName">PRICE_UOM</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">106</property>
                    <property name="name">SHOWSCHEDULE</property>
                    <property name="nativeName">SHOWSCHEDULE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">107</property>
                    <property name="name">SHOWQTY</property>
                    <property name="nativeName">SHOWQTY</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">108</property>
                    <property name="name">SHOWSTRIKEUNIT</property>
                    <property name="nativeName">SHOWSTRIKEUNIT</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">109</property>
                    <property name="name">SHOWFLOATING</property>
                    <property name="nativeName">SHOWFLOATING</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">110</property>
                    <property name="name">PREMIUMGRID</property>
                    <property name="nativeName">PREMIUMGRID</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">111</property>
                    <property name="name">SHOWDAYCONVENTIONS</property>
                    <property name="nativeName">SHOWDAYCONVENTIONS</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">112</property>
                    <property name="name">SHOWBUYERSELLER</property>
                    <property name="nativeName">SHOWBUYERSELLER</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">113</property>
                    <property name="name">SHOWFIRMNESS</property>
                    <property name="nativeName">SHOWFIRMNESS</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">114</property>
                    <property name="name">SHOWPUTCALL</property>
                    <property name="nativeName">SHOWPUTCALL</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">115</property>
                    <property name="name">SHOWCOMMODITY</property>
                    <property name="nativeName">SHOWCOMMODITY</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">116</property>
                    <property name="name">DELIVERYTYPE</property>
                    <property name="nativeName">DELIVERYTYPE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">117</property>
                    <property name="name">SHOWCALCAGENT</property>
                    <property name="nativeName">SHOWCALCAGENT</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">118</property>
                    <property name="name">SHOWSETTLEMENTPRICE</property>
                    <property name="nativeName">SHOWSETTLEMENTPRICE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">119</property>
                    <property name="name">ASSETTYPE</property>
                    <property name="nativeName">ASSETTYPE</property>
                    <property name="dataType">string</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[select * from VW_RPT_CNF_HDR where TNUM = ?]]></xml-property>
            <xml-property name="designerValues"><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<model:DesignValues xmlns:design="http://www.eclipse.org/datatools/connectivity/oda/design" xmlns:model="http://www.eclipse.org/birt/report/model/adapter/odaModel">
  <Version>2.0</Version>
  <design:ResultSets derivedMetaData="true">
    <design:resultSetDefinitions>
      <design:resultSetColumns>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C0_B_S</design:name>
              <design:position>1</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>1</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C0_B_S</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C0_B_S</design:label>
            <design:formattingHints>
              <design:displaySize>1</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C1_CDY1_ATTR1</design:name>
              <design:position>2</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C1_CDY1_ATTR1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C1_CDY1_ATTR1</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C2_CDYDESC1</design:name>
              <design:position>3</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>60</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C2_CDYDESC1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C2_CDYDESC1</design:label>
            <design:formattingHints>
              <design:displaySize>60</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C4_FIX_METHD1</design:name>
              <design:position>4</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>13</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C4_FIX_METHD1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C4_FIX_METHD1</design:label>
            <design:formattingHints>
              <design:displaySize>13</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C5_MKT1</design:name>
              <design:position>5</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>14</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C5_MKT1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C5_MKT1</design:label>
            <design:formattingHints>
              <design:displaySize>14</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C6_PSET1</design:name>
              <design:position>6</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C6_PSET1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C6_PSET1</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C8_AMT_PERIOD</design:name>
              <design:position>7</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C8_AMT_PERIOD</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C8_AMT_PERIOD</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C10_ZKEY</design:name>
              <design:position>8</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C10_ZKEY</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C10_ZKEY</design:label>
            <design:formattingHints>
              <design:displaySize>11</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C11_NUMBLKS</design:name>
              <design:position>9</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C11_NUMBLKS</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C11_NUMBLKS</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C12_BRO_DESCR</design:name>
              <design:position>10</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>128</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C12_BRO_DESCR</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C12_BRO_DESCR</design:label>
            <design:formattingHints>
              <design:displaySize>128</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C13_BROKER</design:name>
              <design:position>11</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C13_BROKER</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C13_BROKER</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C14_CDYDESC1</design:name>
              <design:position>12</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>60</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C14_CDYDESC1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C14_CDYDESC1</design:label>
            <design:formattingHints>
              <design:displaySize>60</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C15_CDYDESC2</design:name>
              <design:position>13</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>60</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C15_CDYDESC2</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C15_CDYDESC2</design:label>
            <design:formattingHints>
              <design:displaySize>60</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C16_CDY1</design:name>
              <design:position>14</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>6</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C16_CDY1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C16_CDY1</design:label>
            <design:formattingHints>
              <design:displaySize>6</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C17_CDYDESC2</design:name>
              <design:position>15</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>60</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C17_CDYDESC2</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C17_CDYDESC2</design:label>
            <design:formattingHints>
              <design:displaySize>60</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C18_CDY1</design:name>
              <design:position>16</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>6</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C18_CDY1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C18_CDY1</design:label>
            <design:formattingHints>
              <design:displaySize>6</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C19_CDY2</design:name>
              <design:position>17</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>6</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C19_CDY2</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C19_CDY2</design:label>
            <design:formattingHints>
              <design:displaySize>6</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C20_CPTY</design:name>
              <design:position>18</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C20_CPTY</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C20_CPTY</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C21_CPTY_ADDR1</design:name>
              <design:position>19</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C21_CPTY_ADDR1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C21_CPTY_ADDR1</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C22_CPTY_ADDR2</design:name>
              <design:position>20</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C22_CPTY_ADDR2</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C22_CPTY_ADDR2</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C23_CPTY_ADDR3</design:name>
              <design:position>21</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C23_CPTY_ADDR3</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C23_CPTY_ADDR3</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C24_CPTY_ADDR4</design:name>
              <design:position>22</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C24_CPTY_ADDR4</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C24_CPTY_ADDR4</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C25_CPTY_DESCR</design:name>
              <design:position>23</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>128</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C25_CPTY_DESCR</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C25_CPTY_DESCR</design:label>
            <design:formattingHints>
              <design:displaySize>128</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C26_CPTY_FAX</design:name>
              <design:position>24</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>24</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C26_CPTY_FAX</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C26_CPTY_FAX</design:label>
            <design:formattingHints>
              <design:displaySize>24</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C27_CPTY_PHONE</design:name>
              <design:position>25</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>24</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C27_CPTY_PHONE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C27_CPTY_PHONE</design:label>
            <design:formattingHints>
              <design:displaySize>24</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C28_CPTY_EMAIL</design:name>
              <design:position>26</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>50</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C28_CPTY_EMAIL</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C28_CPTY_EMAIL</design:label>
            <design:formattingHints>
              <design:displaySize>50</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C29_DELDATEEND_STR</design:name>
              <design:position>27</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C29_DELDATEEND_STR</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C29_DELDATEEND_STR</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C30_DELDATESTART_STR</design:name>
              <design:position>28</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C30_DELDATESTART_STR</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C30_DELDATESTART_STR</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C31_DELDATEEND_STR</design:name>
              <design:position>29</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C31_DELDATEEND_STR</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C31_DELDATEEND_STR</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C32_FIRMNESS</design:name>
              <design:position>30</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>24</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C32_FIRMNESS</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C32_FIRMNESS</design:label>
            <design:formattingHints>
              <design:displaySize>24</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C33_FIX_METHD2</design:name>
              <design:position>31</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>13</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C33_FIX_METHD2</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C33_FIX_METHD2</design:label>
            <design:formattingHints>
              <design:displaySize>13</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C34_FIXMARKET</design:name>
              <design:position>32</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>16</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C34_FIXMARKET</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C34_FIXMARKET</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C35_FLOAT_MULT</design:name>
              <design:position>33</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C35_FLOAT_MULT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C35_FLOAT_MULT</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C36_FLOAT_MULT</design:name>
              <design:position>34</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C36_FLOAT_MULT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C36_FLOAT_MULT</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C37_FLOAT_MULT</design:name>
              <design:position>35</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C37_FLOAT_MULT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C37_FLOAT_MULT</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C39_HOUSE_ADDR1</design:name>
              <design:position>36</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C39_HOUSE_ADDR1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C39_HOUSE_ADDR1</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C40_HOUSE_ADDR2</design:name>
              <design:position>37</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C40_HOUSE_ADDR2</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C40_HOUSE_ADDR2</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C41_HOUSE_ADDR4</design:name>
              <design:position>38</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C41_HOUSE_ADDR4</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C41_HOUSE_ADDR4</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C42_HOUSEFULLNAME</design:name>
              <design:position>39</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>128</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C42_HOUSEFULLNAME</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C42_HOUSEFULLNAME</design:label>
            <design:formattingHints>
              <design:displaySize>128</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C44_HOUSE_ID</design:name>
              <design:position>40</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C44_HOUSE_ID</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C44_HOUSE_ID</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C47_MA_DESCRIPTION</design:name>
              <design:position>41</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C47_MA_DESCRIPTION</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C47_MA_DESCRIPTION</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C48_MA_EFFECTIVE_DT</design:name>
              <design:position>42</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>93</design:nativeDataTypeCode>
            <design:precision>7</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C48_MA_EFFECTIVE_DT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C48_MA_EFFECTIVE_DT</design:label>
            <design:formattingHints>
              <design:displaySize>7</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C49_MA_EXPIRATION_DT</design:name>
              <design:position>43</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>93</design:nativeDataTypeCode>
            <design:precision>7</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C49_MA_EXPIRATION_DT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C49_MA_EXPIRATION_DT</design:label>
            <design:formattingHints>
              <design:displaySize>7</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C50_MANUM</design:name>
              <design:position>44</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>15</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C50_MANUM</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C50_MANUM</design:label>
            <design:formattingHints>
              <design:displaySize>15</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C51_MKT1</design:name>
              <design:position>45</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>14</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C51_MKT1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C51_MKT1</design:label>
            <design:formattingHints>
              <design:displaySize>14</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C52_MKT2</design:name>
              <design:position>46</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>6</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C52_MKT2</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C52_MKT2</design:label>
            <design:formattingHints>
              <design:displaySize>6</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C53_OPTMODEL</design:name>
              <design:position>47</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C53_OPTMODEL</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C53_OPTMODEL</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C54_OPT_TYPE</design:name>
              <design:position>48</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>12</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C54_OPT_TYPE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C54_OPT_TYPE</design:label>
            <design:formattingHints>
              <design:displaySize>12</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C61_PRC_UNIT</design:name>
              <design:position>49</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C61_PRC_UNIT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C61_PRC_UNIT</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C62_PREM_DATE</design:name>
              <design:position>50</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>93</design:nativeDataTypeCode>
            <design:precision>7</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C62_PREM_DATE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C62_PREM_DATE</design:label>
            <design:formattingHints>
              <design:displaySize>7</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C63_PREM_PERIOD</design:name>
              <design:position>51</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C63_PREM_PERIOD</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C63_PREM_PERIOD</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C64_PREM_RATE</design:name>
              <design:position>52</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C64_PREM_RATE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C64_PREM_RATE</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C65_PREM_UNIT</design:name>
              <design:position>53</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C65_PREM_UNIT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C65_PREM_UNIT</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C66_PREMIUM</design:name>
              <design:position>54</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C66_PREMIUM</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C66_PREMIUM</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C67_STRIKE</design:name>
              <design:position>55</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C67_STRIKE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C67_STRIKE</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C69_PRICE_STRIKE</design:name>
              <design:position>56</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C69_PRICE_STRIKE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C69_PRICE_STRIKE</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C70_PRC_UNIT</design:name>
              <design:position>57</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C70_PRC_UNIT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C70_PRC_UNIT</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C74_PUTCALL</design:name>
              <design:position>58</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>4</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C74_PUTCALL</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C74_PUTCALL</design:label>
            <design:formattingHints>
              <design:displaySize>4</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C76_AMT_PERIOD</design:name>
              <design:position>59</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C76_AMT_PERIOD</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C76_AMT_PERIOD</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C77_UOM</design:name>
              <design:position>60</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C77_UOM</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C77_UOM</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C80_DELDATESTART_STR</design:name>
              <design:position>61</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C80_DELDATESTART_STR</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C80_DELDATESTART_STR</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C81_DELDATESTART_STR</design:name>
              <design:position>62</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C81_DELDATESTART_STR</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C81_DELDATESTART_STR</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C82_PRICE_STRIKE</design:name>
              <design:position>63</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C82_PRICE_STRIKE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C82_PRICE_STRIKE</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C85_TNUM</design:name>
              <design:position>64</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>6</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C85_TNUM</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C85_TNUM</design:label>
            <design:formattingHints>
              <design:displaySize>6</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C87_TRADE_DATE</design:name>
              <design:position>65</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>93</design:nativeDataTypeCode>
            <design:precision>7</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C87_TRADE_DATE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C87_TRADE_DATE</design:label>
            <design:formattingHints>
              <design:displaySize>7</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C88_TRADE_TEMPLATE</design:name>
              <design:position>66</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C88_TRADE_TEMPLATE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C88_TRADE_TEMPLATE</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C89_TRADE_TEMPLATE</design:name>
              <design:position>67</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C89_TRADE_TEMPLATE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C89_TRADE_TEMPLATE</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C90_TRADE_TYPE2</design:name>
              <design:position>68</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>31</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C90_TRADE_TYPE2</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C90_TRADE_TYPE2</design:label>
            <design:formattingHints>
              <design:displaySize>31</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C92_ZKEY</design:name>
              <design:position>69</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C92_ZKEY</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C92_ZKEY</design:label>
            <design:formattingHints>
              <design:displaySize>11</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
      </design:resultSetColumns>
      <design:criteria/>
    </design:resultSetDefinitions>
  </design:ResultSets>
</model:DesignValues>]]></xml-property>
        </oda-data-set>
        <oda-data-set extensionID="org.eclipse.birt.report.data.oda.jdbc.JdbcSelectDataSet" name="Data Set 2" id="328">
            <list-property name="columnHints">
                <structure>
                    <property name="columnName">KEY_TNUM</property>
                    <property name="alias">KEY_TNUM</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">KEY_TNUM</text-property>
                    <text-property name="heading">KEY_TNUM</text-property>
                </structure>
                <structure>
                    <property name="columnName">PAYDATE</property>
                    <property name="alias">PAYDATE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PAYDATE</text-property>
                    <text-property name="heading">PAYDATE</text-property>
                </structure>
                <structure>
                    <property name="columnName">DELDAY</property>
                    <property name="alias">DELDAY</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">DELDAY</text-property>
                    <text-property name="heading">DELDAY</text-property>
                </structure>
                <structure>
                    <property name="columnName">HFLAG</property>
                    <property name="alias">HFLAG</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">HFLAG</text-property>
                    <text-property name="heading">HFLAG</text-property>
                </structure>
                <structure>
                    <property name="columnName">PERIOD</property>
                    <property name="alias">PERIOD</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">PERIOD</text-property>
                    <text-property name="heading">PERIOD</text-property>
                </structure>
                <structure>
                    <property name="columnName">DDAY</property>
                    <property name="alias">DDAY</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">DDAY</text-property>
                    <text-property name="heading">DDAY</text-property>
                </structure>
                <structure>
                    <property name="columnName">TIMES</property>
                    <property name="alias">TIMES</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">TIMES</text-property>
                    <text-property name="heading">TIMES</text-property>
                </structure>
                <structure>
                    <property name="columnName">TIMEE</property>
                    <property name="alias">TIMEE</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">TIMEE</text-property>
                    <text-property name="heading">TIMEE</text-property>
                </structure>
                <structure>
                    <property name="columnName">TIMES_</property>
                    <property name="alias">TIMES_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TIMES_</text-property>
                    <text-property name="heading">TIMES_</text-property>
                </structure>
                <structure>
                    <property name="columnName">TIMEE_</property>
                    <property name="alias">TIMEE_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TIMEE_</text-property>
                    <text-property name="heading">TIMEE_</text-property>
                </structure>
                <structure>
                    <property name="columnName">LONGDAYFLAG</property>
                    <property name="alias">LONGDAYFLAG</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">LONGDAYFLAG</text-property>
                    <text-property name="heading">LONGDAYFLAG</text-property>
                </structure>
                <structure>
                    <property name="columnName">QTY</property>
                    <property name="alias">QTY</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">QTY</text-property>
                    <text-property name="heading">QTY</text-property>
                </structure>
                <structure>
                    <property name="columnName">PRC</property>
                    <property name="alias">PRC</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">PRC</text-property>
                    <text-property name="heading">PRC</text-property>
                </structure>
                <structure>
                    <property name="columnName">POWSEG</property>
                    <property name="alias">POWSEG</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">POWSEG</text-property>
                    <text-property name="heading">POWSEG</text-property>
                </structure>
                <structure>
                    <property name="columnName">ENDDAY</property>
                    <property name="alias">ENDDAY</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">ENDDAY</text-property>
                    <text-property name="heading">ENDDAY</text-property>
                </structure>
                <structure>
                    <property name="columnName">AMT_PERIOD</property>
                    <property name="alias">AMT_PERIOD</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">AMT_PERIOD</text-property>
                    <text-property name="heading">AMT_PERIOD</text-property>
                </structure>
                <structure>
                    <property name="columnName">AMT_UNIT</property>
                    <property name="alias">AMT_UNIT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">AMT_UNIT</text-property>
                    <text-property name="heading">AMT_UNIT</text-property>
                </structure>
                <structure>
                    <property name="columnName">CCY</property>
                    <property name="alias">CCY</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CCY</text-property>
                    <text-property name="heading">CCY</text-property>
                </structure>
                <structure>
                    <property name="columnName">UNIT</property>
                    <property name="alias">UNIT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">UNIT</text-property>
                    <text-property name="heading">UNIT</text-property>
                </structure>
                <structure>
                    <property name="columnName">TS</property>
                    <property name="alias">TS</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TS</text-property>
                    <text-property name="heading">TS</text-property>
                </structure>
                <structure>
                    <property name="columnName">TE</property>
                    <property name="alias">TE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TE</text-property>
                    <text-property name="heading">TE</text-property>
                </structure>
                <structure>
                    <property name="columnName">M2</property>
                    <property name="alias">M2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">M2</text-property>
                    <text-property name="heading">M2</text-property>
                </structure>
                <structure>
                    <property name="columnName">TS_STARTTIME</property>
                    <property name="alias">TS_STARTTIME</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TS_STARTTIME</text-property>
                    <text-property name="heading">TS_STARTTIME</text-property>
                </structure>
                <structure>
                    <property name="columnName">TS_ENDTIME</property>
                    <property name="alias">TS_ENDTIME</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TS_ENDTIME</text-property>
                    <text-property name="heading">TS_ENDTIME</text-property>
                </structure>
                <structure>
                    <property name="columnName">RTOT</property>
                    <property name="alias">RTOT</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">RTOT</text-property>
                    <text-property name="heading">RTOT</text-property>
                </structure>
                <structure>
                    <property name="columnName">AUD_REFRESH</property>
                    <property name="alias">AUD_REFRESH</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">AUD_REFRESH</text-property>
                    <text-property name="heading">AUD_REFRESH</text-property>
                </structure>
                <structure>
                    <property name="columnName">RID</property>
                    <property name="alias">RID</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">RID</text-property>
                    <text-property name="heading">RID</text-property>
                </structure>
                <structure>
                    <property name="columnName">AUD_GEN</property>
                    <property name="alias">AUD_GEN</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">AUD_GEN</text-property>
                    <text-property name="heading">AUD_GEN</text-property>
                </structure>
                <structure>
                    <property name="columnName">QTYTOT</property>
                    <property name="alias">QTYTOT</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">QTYTOT</text-property>
                    <text-property name="heading">QTYTOT</text-property>
                </structure>
                <structure>
                    <property name="columnName">INVOICE_AMOUNT</property>
                    <property name="alias">INVOICE_AMOUNT</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">INVOICE_AMOUNT</text-property>
                    <text-property name="heading">INVOICE_AMOUNT</text-property>
                </structure>
                <structure>
                    <property name="columnName">QTY_ABS</property>
                    <property name="alias">QTY_ABS</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">QTY_ABS</text-property>
                    <text-property name="heading">QTY_ABS</text-property>
                </structure>
                <structure>
                    <property name="columnName">INVOICE_AMOUNT_ABS</property>
                    <property name="alias">INVOICE_AMOUNT_ABS</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">INVOICE_AMOUNT_ABS</text-property>
                    <text-property name="heading">INVOICE_AMOUNT_ABS</text-property>
                </structure>
                <structure>
                    <property name="columnName">ZKEY</property>
                    <property name="alias">ZKEY</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">ZKEY</text-property>
                    <text-property name="heading">ZKEY</text-property>
                </structure>
                <structure>
                    <property name="columnName">CAP_ABS</property>
                    <property name="alias">CAP_ABS</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">CAP_ABS</text-property>
                    <text-property name="heading">CAP_ABS</text-property>
                </structure>
                <structure>
                    <property name="columnName">DELDAYS</property>
                    <property name="alias">DELDAYS</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">DELDAYS</text-property>
                    <text-property name="heading">DELDAYS</text-property>
                </structure>
                <structure>
                    <property name="columnName">EXPIRY</property>
                    <property name="alias">EXPIRY</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">EXPIRY</text-property>
                    <text-property name="heading">EXPIRY</text-property>
                </structure>
            </list-property>
            <list-property name="parameters">
                <structure>
                    <property name="name">param_1</property>
                    <property name="paramName">TNUM</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                    <property name="position">1</property>
                    <property name="isOptional">true</property>
                    <property name="allowNull">true</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
            </list-property>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">KEY_TNUM</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">2</property>
                        <property name="name">PAYDATE</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">3</property>
                        <property name="name">DELDAY</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">4</property>
                        <property name="name">HFLAG</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">5</property>
                        <property name="name">PERIOD</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">6</property>
                        <property name="name">DDAY</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">7</property>
                        <property name="name">TIMES</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">8</property>
                        <property name="name">TIMEE</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">9</property>
                        <property name="name">TIMES_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">10</property>
                        <property name="name">TIMEE_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">11</property>
                        <property name="name">LONGDAYFLAG</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">12</property>
                        <property name="name">QTY</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">13</property>
                        <property name="name">PRC</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">14</property>
                        <property name="name">POWSEG</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">15</property>
                        <property name="name">ENDDAY</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">16</property>
                        <property name="name">AMT_PERIOD</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">17</property>
                        <property name="name">AMT_UNIT</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">18</property>
                        <property name="name">CCY</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">19</property>
                        <property name="name">UNIT</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">20</property>
                        <property name="name">TS</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">21</property>
                        <property name="name">TE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">22</property>
                        <property name="name">M2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">23</property>
                        <property name="name">TS_STARTTIME</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">24</property>
                        <property name="name">TS_ENDTIME</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">25</property>
                        <property name="name">RTOT</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">26</property>
                        <property name="name">AUD_REFRESH</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">27</property>
                        <property name="name">RID</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">28</property>
                        <property name="name">AUD_GEN</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">29</property>
                        <property name="name">QTYTOT</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">30</property>
                        <property name="name">INVOICE_AMOUNT</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">31</property>
                        <property name="name">QTY_ABS</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">32</property>
                        <property name="name">INVOICE_AMOUNT_ABS</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">33</property>
                        <property name="name">ZKEY</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">34</property>
                        <property name="name">CAP_ABS</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">35</property>
                        <property name="name">DELDAYS</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">36</property>
                        <property name="name">EXPIRY</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">37</property>
                        <property name="name">PRICE_TRD</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">38</property>
                        <property name="name">FLOAT_MULT</property>
                        <property name="dataType">decimal</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">Data Source</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">KEY_TNUM</property>
                    <property name="nativeName">KEY_TNUM</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">2</property>
                    <property name="name">PAYDATE</property>
                    <property name="nativeName">PAYDATE</property>
                    <property name="dataType">date-time</property>
                </structure>
                <structure>
                    <property name="position">3</property>
                    <property name="name">DELDAY</property>
                    <property name="nativeName">DELDAY</property>
                    <property name="dataType">date-time</property>
                </structure>
                <structure>
                    <property name="position">4</property>
                    <property name="name">HFLAG</property>
                    <property name="nativeName">HFLAG</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">5</property>
                    <property name="name">PERIOD</property>
                    <property name="nativeName">PERIOD</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">6</property>
                    <property name="name">DDAY</property>
                    <property name="nativeName">DDAY</property>
                    <property name="dataType">date-time</property>
                </structure>
                <structure>
                    <property name="position">7</property>
                    <property name="name">TIMES</property>
                    <property name="nativeName">TIMES</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">8</property>
                    <property name="name">TIMEE</property>
                    <property name="nativeName">TIMEE</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">9</property>
                    <property name="name">TIMES_</property>
                    <property name="nativeName">TIMES_</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">10</property>
                    <property name="name">TIMEE_</property>
                    <property name="nativeName">TIMEE_</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">11</property>
                    <property name="name">LONGDAYFLAG</property>
                    <property name="nativeName">LONGDAYFLAG</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">12</property>
                    <property name="name">QTY</property>
                    <property name="nativeName">QTY</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">13</property>
                    <property name="name">PRC</property>
                    <property name="nativeName">PRC</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">14</property>
                    <property name="name">POWSEG</property>
                    <property name="nativeName">POWSEG</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">15</property>
                    <property name="name">ENDDAY</property>
                    <property name="nativeName">ENDDAY</property>
                    <property name="dataType">date-time</property>
                </structure>
                <structure>
                    <property name="position">16</property>
                    <property name="name">AMT_PERIOD</property>
                    <property name="nativeName">AMT_PERIOD</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">17</property>
                    <property name="name">AMT_UNIT</property>
                    <property name="nativeName">AMT_UNIT</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">18</property>
                    <property name="name">CCY</property>
                    <property name="nativeName">CCY</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">19</property>
                    <property name="name">UNIT</property>
                    <property name="nativeName">UNIT</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">20</property>
                    <property name="name">TS</property>
                    <property name="nativeName">TS</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">21</property>
                    <property name="name">TE</property>
                    <property name="nativeName">TE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">22</property>
                    <property name="name">M2</property>
                    <property name="nativeName">M2</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">23</property>
                    <property name="name">TS_STARTTIME</property>
                    <property name="nativeName">TS_STARTTIME</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">24</property>
                    <property name="name">TS_ENDTIME</property>
                    <property name="nativeName">TS_ENDTIME</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">25</property>
                    <property name="name">RTOT</property>
                    <property name="nativeName">RTOT</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">26</property>
                    <property name="name">AUD_REFRESH</property>
                    <property name="nativeName">AUD_REFRESH</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">27</property>
                    <property name="name">RID</property>
                    <property name="nativeName">RID</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">28</property>
                    <property name="name">AUD_GEN</property>
                    <property name="nativeName">AUD_GEN</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">29</property>
                    <property name="name">QTYTOT</property>
                    <property name="nativeName">QTYTOT</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">30</property>
                    <property name="name">INVOICE_AMOUNT</property>
                    <property name="nativeName">INVOICE_AMOUNT</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">31</property>
                    <property name="name">QTY_ABS</property>
                    <property name="nativeName">QTY_ABS</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">32</property>
                    <property name="name">INVOICE_AMOUNT_ABS</property>
                    <property name="nativeName">INVOICE_AMOUNT_ABS</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">33</property>
                    <property name="name">ZKEY</property>
                    <property name="nativeName">ZKEY</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">34</property>
                    <property name="name">CAP_ABS</property>
                    <property name="nativeName">CAP_ABS</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">35</property>
                    <property name="name">DELDAYS</property>
                    <property name="nativeName">DELDAYS</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">36</property>
                    <property name="name">EXPIRY</property>
                    <property name="nativeName">EXPIRY</property>
                    <property name="dataType">date-time</property>
                </structure>
                <structure>
                    <property name="position">37</property>
                    <property name="name">PRICE_TRD</property>
                    <property name="nativeName">PRICE_TRD</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">38</property>
                    <property name="name">FLOAT_MULT</property>
                    <property name="nativeName">FLOAT_MULT</property>
                    <property name="dataType">decimal</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[select * from VW_RPT_CNF_DETAILS where KEY_TNUM = ?]]></xml-property>
            <xml-property name="designerValues"><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<model:DesignValues xmlns:design="http://www.eclipse.org/datatools/connectivity/oda/design" xmlns:model="http://www.eclipse.org/birt/report/model/adapter/odaModel">
  <Version>2.0</Version>
  <design:ResultSets derivedMetaData="true">
    <design:resultSetDefinitions>
      <design:resultSetColumns>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>KEY_TNUM</design:name>
              <design:position>1</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>6</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>KEY_TNUM</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>KEY_TNUM</design:label>
            <design:formattingHints>
              <design:displaySize>6</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>PAYDATE</design:name>
              <design:position>2</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>93</design:nativeDataTypeCode>
            <design:precision>7</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>PAYDATE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>PAYDATE</design:label>
            <design:formattingHints>
              <design:displaySize>7</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>DELDAY</design:name>
              <design:position>3</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>93</design:nativeDataTypeCode>
            <design:precision>7</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>DELDAY</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>DELDAY</design:label>
            <design:formattingHints>
              <design:displaySize>7</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>HFLAG</design:name>
              <design:position>4</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>1</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>HFLAG</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>HFLAG</design:label>
            <design:formattingHints>
              <design:displaySize>1</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>PERIOD</design:name>
              <design:position>5</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>PERIOD</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>PERIOD</design:label>
            <design:formattingHints>
              <design:displaySize>11</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>DDAY</design:name>
              <design:position>6</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>93</design:nativeDataTypeCode>
            <design:precision>7</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>DDAY</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>DDAY</design:label>
            <design:formattingHints>
              <design:displaySize>7</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>TIMES</design:name>
              <design:position>7</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>TIMES</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>TIMES</design:label>
            <design:formattingHints>
              <design:displaySize>11</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>TIMEE</design:name>
              <design:position>8</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>TIMEE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>TIMEE</design:label>
            <design:formattingHints>
              <design:displaySize>11</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>TIMES_</design:name>
              <design:position>9</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>TIMES_</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>TIMES_</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>TIMEE_</design:name>
              <design:position>10</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>TIMEE_</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>TIMEE_</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>LONGDAYFLAG</design:name>
              <design:position>11</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>1</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>LONGDAYFLAG</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>LONGDAYFLAG</design:label>
            <design:formattingHints>
              <design:displaySize>1</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>QTY</design:name>
              <design:position>12</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>QTY</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>QTY</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>PRC</design:name>
              <design:position>13</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>PRC</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>PRC</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>POWSEG</design:name>
              <design:position>14</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>20</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>POWSEG</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>POWSEG</design:label>
            <design:formattingHints>
              <design:displaySize>20</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>ENDDAY</design:name>
              <design:position>15</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>93</design:nativeDataTypeCode>
            <design:precision>7</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>ENDDAY</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>ENDDAY</design:label>
            <design:formattingHints>
              <design:displaySize>7</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>AMT_PERIOD</design:name>
              <design:position>16</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>AMT_PERIOD</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>AMT_PERIOD</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>AMT_UNIT</design:name>
              <design:position>17</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>AMT_UNIT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>AMT_UNIT</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>CCY</design:name>
              <design:position>18</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>6</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>CCY</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>CCY</design:label>
            <design:formattingHints>
              <design:displaySize>6</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>UNIT</design:name>
              <design:position>19</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>UNIT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>UNIT</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>TS</design:name>
              <design:position>20</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>TS</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>TS</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>TE</design:name>
              <design:position>21</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>TE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>TE</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>M2</design:name>
              <design:position>22</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>6</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>M2</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>M2</design:label>
            <design:formattingHints>
              <design:displaySize>6</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>TS_STARTTIME</design:name>
              <design:position>23</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>30</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>TS_STARTTIME</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>TS_STARTTIME</design:label>
            <design:formattingHints>
              <design:displaySize>30</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>TS_ENDTIME</design:name>
              <design:position>24</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>30</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>TS_ENDTIME</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>TS_ENDTIME</design:label>
            <design:formattingHints>
              <design:displaySize>30</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>RTOT</design:name>
              <design:position>25</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>RTOT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>RTOT</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>AUD_REFRESH</design:name>
              <design:position>26</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>AUD_REFRESH</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>AUD_REFRESH</design:label>
            <design:formattingHints>
              <design:displaySize>11</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>RID</design:name>
              <design:position>27</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>RID</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>RID</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>AUD_GEN</design:name>
              <design:position>28</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>5</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>AUD_GEN</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>AUD_GEN</design:label>
            <design:formattingHints>
              <design:displaySize>6</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>QTYTOT</design:name>
              <design:position>29</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>QTYTOT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>QTYTOT</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>INVOICE_AMOUNT</design:name>
              <design:position>30</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>INVOICE_AMOUNT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>INVOICE_AMOUNT</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>QTY_ABS</design:name>
              <design:position>31</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>QTY_ABS</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>QTY_ABS</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>INVOICE_AMOUNT_ABS</design:name>
              <design:position>32</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>INVOICE_AMOUNT_ABS</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>INVOICE_AMOUNT_ABS</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>ZKEY</design:name>
              <design:position>33</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>ZKEY</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>ZKEY</design:label>
            <design:formattingHints>
              <design:displaySize>11</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>CAP_ABS</design:name>
              <design:position>34</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>CAP_ABS</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>CAP_ABS</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>DELDAYS</design:name>
              <design:position>35</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>8</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>DELDAYS</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>DELDAYS</design:label>
            <design:formattingHints>
              <design:displaySize>8</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>EXPIRY</design:name>
              <design:position>36</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>93</design:nativeDataTypeCode>
            <design:precision>7</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>EXPIRY</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>EXPIRY</design:label>
            <design:formattingHints>
              <design:displaySize>7</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
      </design:resultSetColumns>
      <design:criteria/>
    </design:resultSetDefinitions>
  </design:ResultSets>
</model:DesignValues>]]></xml-property>
        </oda-data-set>
    </data-sets>
    <styles>
        <style name="report" id="736">
            <structure name="dateTimeFormat">
                <property name="category">Custom</property>
                <property name="pattern">MM/dd/yyyy hh:mm:s a</property>
            </structure>
        </style>
    </styles>
    <page-setup>
        <simple-master-page name="Simple MasterPage" id="2">
            <page-footer>
                <text id="3">
                    <property name="contentType">html</property>
                    <text-property name="content"><![CDATA[<value-of>new Date()</value-of>]]></text-property>
                </text>
            </page-footer>
        </simple-master-page>
    </page-setup>
    <body>
        <grid name="Logo and Sender Information" id="8">
            <property name="height">1.0833333333333333in</property>
            <property name="width">7.947916666666667in</property>
            <column id="9">
                <property name="width">2.5625in</property>
            </column>
            <column id="10">
                <property name="width">5.385416666666667in</property>
            </column>
            <row id="11">
                <property name="height">1.0833333333333333in</property>
                <cell id="12">
                    <image id="244">
                        <property name="height">1in</property>
                        <property name="width">2.1354166666666665in</property>
                        <property name="source">file</property>
                        <expression name="uri" type="constant">direct-energy.png</expression>
                    </image>
                </cell>
                <cell id="13">
                    <data id="329">
                        <property name="fontSize">10pt</property>
                        <property name="textAlign">right</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">HOUSEFULLNAME</property>
                                <text-property name="displayName">HOUSEFULLNAME</text-property>
                                <expression name="expression" type="javascript">dataSetRow["HOUSEFULLNAME"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">HOUSEFULLNAME</property>
                    </data>
                    <data id="330">
                        <property name="fontSize">10pt</property>
                        <property name="textAlign">right</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">HOUSE_ADDR1</property>
                                <text-property name="displayName">HOUSE_ADDR1</text-property>
                                <expression name="expression" type="javascript">dataSetRow["HOUSE_ADDR1"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">HOUSE_ADDR1</property>
                    </data>
                    <data id="331">
                        <property name="fontSize">10pt</property>
                        <property name="textAlign">right</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">HOUSE_ADDR2</property>
                                <text-property name="displayName">HOUSE_ADDR2</text-property>
                                <expression name="expression" type="javascript">dataSetRow["HOUSE_ADDR2"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">HOUSE_ADDR2</property>
                    </data>
                </cell>
            </row>
        </grid>
        <grid name="Receiver Information" id="246">
            <property name="height">2.986111111111111in</property>
            <property name="width">7.947916666666667in</property>
            <column id="247">
                <property name="width">1.1354166666666667in</property>
            </column>
            <column id="248">
                <property name="width">6.8125in</property>
            </column>
            <row id="249">
                <property name="height">12pt</property>
                <cell id="250">
                    <text id="261">
                        <property name="fontSize">10pt</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[To:]]></text-property>
                    </text>
                </cell>
                <cell id="251">
                    <property name="fontSize">8pt</property>
                    <data id="332">
                        <property name="fontSize">10pt</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">CPTY_DESCR</property>
                                <text-property name="displayName">CPTY_DESCR</text-property>
                                <expression name="expression" type="javascript">dataSetRow["CPTY_DESCR"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">CPTY_DESCR</property>
                    </data>
                </cell>
            </row>
            <row id="252">
                <property name="height">48pt</property>
                <cell id="253">
                    <text id="262">
                        <property name="fontSize">10pt</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[Address:]]></text-property>
                    </text>
                </cell>
                <cell id="254">
                    <property name="fontSize">8pt</property>
                    <data id="333">
                        <property name="fontSize">10pt</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">CPTY_ADDR1</property>
                                <text-property name="displayName">CPTY_ADDR1</text-property>
                                <expression name="expression" type="javascript">dataSetRow["CPTY_ADDR1"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">CPTY_ADDR1</property>
                    </data>
                    <data id="334">
                        <property name="fontSize">10pt</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">CPTY_ADDR2</property>
                                <text-property name="displayName">CPTY_ADDR2</text-property>
                                <expression name="expression" type="javascript">dataSetRow["CPTY_ADDR2"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">CPTY_ADDR2</property>
                    </data>
                    <data id="335">
                        <property name="fontSize">10pt</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">CPTY_ADDR3</property>
                                <text-property name="displayName">CPTY_ADDR3</text-property>
                                <expression name="expression" type="javascript">dataSetRow["CPTY_ADDR3"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">CPTY_ADDR3</property>
                    </data>
                    <data id="336">
                        <property name="fontSize">10pt</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">CPTY_ADDR4</property>
                                <text-property name="displayName">CPTY_ADDR4</text-property>
                                <expression name="expression" type="javascript">dataSetRow["CPTY_ADDR4"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">CPTY_ADDR4</property>
                    </data>
                </cell>
            </row>
            <row id="255">
                <property name="height">12pt</property>
                <cell id="256">
                    <text id="263">
                        <property name="fontSize">10pt</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[Fax:]]></text-property>
                    </text>
                </cell>
                <cell id="257">
                    <data id="337">
                        <property name="fontSize">10pt</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">CPTY</property>
                                <text-property name="displayName">CPTY</text-property>
                                <expression name="expression" type="javascript">dataSetRow["CPTY"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">CPTY</property>
                    </data>
                </cell>
            </row>
            <row id="258">
                <property name="height">12pt</property>
                <cell id="259">
                    <text id="264">
                        <property name="fontSize">10pt</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[Date:]]></text-property>
                    </text>
                </cell>
                <cell id="260">
                    <data id="352">
                        <property name="fontSize">10pt</property>
                        <structure name="dateTimeFormat">
                            <property name="category">Long Date</property>
                            <property name="pattern">Long Date</property>
                        </structure>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">REPORT_DATE</property>
                                <text-property name="displayName">REPORT_DATE</text-property>
                                <expression name="expression" type="javascript">dataSetRow["REPORT_DATE"]</expression>
                                <property name="dataType">date-time</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">REPORT_DATE</property>
                    </data>
                </cell>
            </row>
        </grid>
        <grid name="Ticket Number" id="271">
            <property name="width">7.951388888888889in</property>
            <column id="272">
                <property name="width">6.347222222222222in</property>
            </column>
            <column id="273"/>
            <row id="274">
                <cell id="275"/>
                <cell id="276">
                    <text id="277">
                        <property name="fontSize">10pt</property>
                        <property name="display">inline</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[Tkt Number:]]></text-property>
                    </text>
                    <data id="584">
                        <property name="fontSize">10pt</property>
                        <property name="display">inline</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">TNUM</property>
                                <text-property name="displayName">TNUM</text-property>
                                <expression name="expression" type="javascript">dataSetRow["TNUM"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">TNUM</property>
                    </data>
                    <text id="751">
                        <property name="display">inline</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[ / ]]></text-property>
                    </text>
                    <data id="724">
                        <property name="display">inline</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">ZKEY_</property>
                                <text-property name="displayName">ZKEY_</text-property>
                                <expression name="expression" type="javascript">dataSetRow["ZKEY_"]</expression>
                                <property name="dataType">decimal</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">ZKEY_</property>
                    </data>
                </cell>
            </row>
        </grid>
        <text id="581">
            <property name="contentType">auto</property>
        </text>
        <grid name="Legal Title" id="608">
            <column id="609"/>
            <row id="610">
                <cell id="611">
                    <text-data name="Legal Title 1" id="521">
                        <property name="fontSize">10pt</property>
                        <property name="textAlign">center</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">CAPCITYPWR2</property>
                                <text-property name="displayName">CAPCITYPWR2</text-property>
                                <expression name="expression" type="javascript">dataSetRow["CAPCITYPWR2"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <expression name="valueExpr">"CONFIDENTIAL\n"</expression>
                        <property name="contentType">html</property>
                    </text-data>
                </cell>
            </row>
            <row id="612">
                <cell id="613">
                    <data id="750">
                        <property name="textAlign">center</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">CONF__TITLE</property>
                                <text-property name="displayName">CONF__TITLE</text-property>
                                <expression name="expression" type="javascript">dataSetRow["CONF__TITLE"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">CONF__TITLE</property>
                    </data>
                </cell>
            </row>
        </grid>
        <text id="590">
            <property name="contentType">auto</property>
            <text-property name="content"><![CDATA[ ]]></text-property>
        </text>
        <grid name="Operative Clause" id="616">
            <column id="617"/>
            <row id="618">
                <cell id="619">
                    <text-data name="Operative Clause 1" id="587">
                        <property name="display">inline</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">CPTY_DESCR</property>
                                <text-property name="displayName">CPTY_DESCR</text-property>
                                <expression name="expression" type="javascript">dataSetRow["CPTY_DESCR"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                            <structure>
                                <property name="name">HOUSEFULLNAME</property>
                                <text-property name="displayName">HOUSEFULLNAME</text-property>
                                <expression name="expression" type="javascript">dataSetRow["HOUSEFULLNAME"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                            <structure>
                                <property name="name">MA_ATTR1</property>
                                <text-property name="displayName">MA_ATTR1</text-property>
                                <expression name="expression" type="javascript">dataSetRow["MA_ATTR1"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                            <structure>
                                <property name="name">MA_DESCRIPTION</property>
                                <text-property name="displayName">MA_DESCRIPTION</text-property>
                                <expression name="expression" type="javascript">dataSetRow["MA_DESCRIPTION"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <expression name="valueExpr">"This Confirmation shall confirm the transaction between " + row["HOUSEFULLNAME"] + "(\"" + row["MA_ATTR1"] + "\") and " + row["CPTY_DESCR"] + " (\"Counterparty\") and, Counterparty together with " + row["MA_ATTR1"] + ", the (\"Parties\")."&#13;
+ "This Confirmation is being provided pursuant to and in accordance with the " + row["MA_DESCRIPTION"] + " (\"the Agreement\") between the Parties dated "</expression>
                        <property name="contentType">html</property>
                    </text-data>
                    <text id="725">
                        <property name="backgroundColor">#FFFFFF</property>
                        <property name="display">inline</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[ ]]></text-property>
                    </text>
                    <data name="Operative Clause 2" id="588">
                        <structure name="dateTimeFormat">
                            <property name="category">Long Date</property>
                            <property name="pattern">Long Date</property>
                        </structure>
                        <property name="display">inline</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">MA_EFFECTIVE_DT</property>
                                <text-property name="displayName">MA_EFFECTIVE_DT</text-property>
                                <expression name="expression" type="javascript">dataSetRow["MA_EFFECTIVE_DT"]</expression>
                                <property name="dataType">date-time</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">MA_EFFECTIVE_DT</property>
                    </data>
                    <text id="726">
                        <property name="backgroundColor">#FFFFFF</property>
                        <property name="display">inline</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[ ]]></text-property>
                    </text>
                    <text-data name="Operative Clause 3" id="100">
                        <property name="fontSize">10pt</property>
                        <property name="display">inline</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">CPTY_DESCR</property>
                                <text-property name="displayName">CPTY_DESCR</text-property>
                                <expression name="expression" type="javascript">dataSetRow["CPTY_DESCR"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                            <structure>
                                <property name="name">HOUSEFULLNAME</property>
                                <text-property name="displayName">HOUSEFULLNAME</text-property>
                                <expression name="expression" type="javascript">dataSetRow["HOUSEFULLNAME"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                            <structure>
                                <property name="name">MA_ATTR1</property>
                                <text-property name="displayName">MA_ATTR1</text-property>
                                <expression name="expression" type="javascript">dataSetRow["MA_ATTR1"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                            <structure>
                                <property name="name">MA_DESCRIPTION</property>
                                <text-property name="displayName">MA_DESCRIPTION</text-property>
                                <expression name="expression" type="javascript">dataSetRow["MA_DESCRIPTION"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                            <structure>
                                <property name="name">MA_EFFECTIVE_DT</property>
                                <text-property name="displayName">MA_EFFECTIVE_DT</text-property>
                                <expression name="expression" type="javascript">dataSetRow["MA_EFFECTIVE_DT"]</expression>
                                <property name="dataType">date-time</property>
                            </structure>
                        </list-property>
                        <expression name="valueExpr">" and constitutes part of and is subject to the terms and provisions of such Agreement. "</expression>
                        <property name="contentType">html</property>
                    </text-data>
                </cell>
            </row>
        </grid>
        <text id="583">
            <property name="contentType">auto</property>
            <text-property name="content"><![CDATA[ ]]></text-property>
        </text>
        <grid name="Derivatives Info" id="278">
            <property name="width">7.947916666666667in</property>
            <column id="279">
                <property name="width">2.78125in</property>
            </column>
            <column id="280">
                <property name="width">5.166666666666667in</property>
            </column>
            <row id="281">
                <cell id="282">
                    <text id="313">
                        <property name="fontSize">10pt</property>
                        <property name="textAlign">left</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[TRADE DATE:]]></text-property>
                    </text>
                </cell>
                <cell id="283">
                    <data id="338">
                        <property name="fontSize">10pt</property>
                        <structure name="dateTimeFormat">
                            <property name="category">Long Date</property>
                            <property name="pattern">Long Date</property>
                        </structure>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">TRADE_DATE</property>
                                <text-property name="displayName">TRADE_DATE</text-property>
                                <expression name="expression" type="javascript">dataSetRow["TRADE_DATE"]</expression>
                                <property name="dataType">date-time</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">TRADE_DATE</property>
                    </data>
                </cell>
            </row>
            <row id="284">
                <cell id="285">
                    <text id="314">
                        <property name="fontSize">10pt</property>
                        <property name="textAlign">left</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[BUYER:]]></text-property>
                    </text>
                </cell>
                <cell id="286">
                    <data id="353">
                        <property name="fontSize">10pt</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">PHYS_BUYER2_ALT</property>
                                <text-property name="displayName">PHYS_BUYER2_ALT</text-property>
                                <expression name="expression" type="javascript">dataSetRow["PHYS_BUYER2_ALT"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">PHYS_BUYER2_ALT</property>
                    </data>
                </cell>
            </row>
            <row id="287">
                <cell id="288">
                    <text id="315">
                        <property name="fontSize">10pt</property>
                        <property name="textAlign">left</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[SELLER:]]></text-property>
                    </text>
                </cell>
                <cell id="289">
                    <data id="354">
                        <property name="fontSize">10pt</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">PHYS_SELLER2_ALT</property>
                                <text-property name="displayName">PHYS_SELLER2_ALT</text-property>
                                <expression name="expression" type="javascript">dataSetRow["PHYS_SELLER2_ALT"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">PHYS_SELLER2_ALT</property>
                    </data>
                </cell>
            </row>
            <row id="290">
                <cell id="291">
                    <text id="317">
                        <property name="fontSize">10pt</property>
                        <property name="textAlign">left</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[PRODUCT TYPE:]]></text-property>
                    </text>
                </cell>
                <cell id="292">
                    <data id="522">
                        <property name="fontSize">10pt</property>
                        <property name="display">inline</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">CAPCITYPWR</property>
                                <text-property name="displayName">CAPCITYPWR</text-property>
                                <expression name="expression" type="javascript">dataSetRow["CAPCITYPWR"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">CAPCITYPWR</property>
                    </data>
                    <text id="523">
                        <property name="fontSize">10pt</property>
                        <property name="display">inline</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[, ]]></text-property>
                    </text>
                    <data id="524">
                        <property name="fontSize">10pt</property>
                        <property name="display">inline</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">TRADETYPEINDEX</property>
                                <text-property name="displayName">TRADETYPEINDEX</text-property>
                                <expression name="expression" type="javascript">dataSetRow["TRADETYPEINDEX"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">TRADETYPEINDEX</property>
                    </data>
                </cell>
            </row>
            <row id="293">
                <cell id="294">
                    <text id="319">
                        <property name="fontSize">10pt</property>
                        <property name="textAlign">left</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[DELIVERY PERIOD:]]></text-property>
                    </text>
                </cell>
                <cell id="295">
                    <data id="343">
                        <property name="fontSize">10pt</property>
                        <structure name="dateTimeFormat">
                            <property name="category">Unformatted</property>
                        </structure>
                        <property name="display">inline</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">DELDATESTART_STR_</property>
                                <text-property name="displayName">DELDATESTART_STR_</text-property>
                                <expression name="expression" type="javascript">dataSetRow["DELDATESTART_STR_"]</expression>
                                <property name="dataType">date-time</property>
                                <property name="allowExport">true</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">DELDATESTART_STR_</property>
                    </data>
                    <text id="344">
                        <property name="display">inline</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[-]]></text-property>
                    </text>
                    <data id="346">
                        <property name="fontSize">10pt</property>
                        <structure name="dateTimeFormat">
                            <property name="category">Unformatted</property>
                        </structure>
                        <property name="display">inline</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">DELDATEEND_STR_</property>
                                <text-property name="displayName">DELDATEEND_STR_</text-property>
                                <expression name="expression" type="javascript">dataSetRow["DELDATEEND_STR_"]</expression>
                                <property name="dataType">date-time</property>
                                <property name="allowExport">true</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">DELDATEEND_STR_</property>
                    </data>
                </cell>
            </row>
            <row id="296">
                <cell id="297">
                    <text id="320">
                        <property name="fontSize">10pt</property>
                        <property name="textAlign">left</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[DELIVERY POINT:]]></text-property>
                    </text>
                </cell>
                <cell id="298">
                    <data id="525">
                        <property name="fontSize">10pt</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">SIDE1</property>
                                <text-property name="displayName">SIDE1</text-property>
                                <expression name="expression" type="javascript">dataSetRow["SIDE1"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">SIDE1</property>
                    </data>
                </cell>
            </row>
            <row id="299">
                <cell id="300">
                    <text id="321">
                        <property name="fontSize">10pt</property>
                        <property name="textAlign">left</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[TOTAL NOTIONAL QUANTITY:]]></text-property>
                    </text>
                </cell>
                <cell id="301">
                    <data id="652">
                        <property name="display">inline</property>
                        <property name="dataSet">Data Set 2</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">QTYTOT</property>
                                <text-property name="displayName">QTYTOT</text-property>
                                <expression name="expression" type="javascript">dataSetRow["QTYTOT"]</expression>
                                <property name="dataType">decimal</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">QTYTOT</property>
                    </data>
                    <data id="727">
                        <property name="display">inline</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">PRC_UNIT</property>
                                <text-property name="displayName">PRC_UNIT</text-property>
                                <expression name="expression" type="javascript">dataSetRow["PRC_UNIT"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">PRC_UNIT</property>
                    </data>
                </cell>
            </row>
            <row id="302">
                <cell id="303">
                    <text id="322">
                        <property name="fontSize">10pt</property>
                        <property name="textAlign">left</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[INDEX PRICE:]]></text-property>
                    </text>
                </cell>
                <cell id="304">
                    <data id="526">
                        <property name="fontSize">10pt</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">SIDE2</property>
                                <text-property name="displayName">SIDE2</text-property>
                                <expression name="expression" type="javascript">dataSetRow["SIDE2"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">SIDE2</property>
                    </data>
                </cell>
            </row>
            <row id="305">
                <cell id="306">
                    <text id="323">
                        <property name="fontSize">10pt</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[BROKER:]]></text-property>
                    </text>
                </cell>
                <cell id="307">
                    <text id="672">
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="visibility">
                            <structure>
                                <property name="format">all</property>
                                <expression name="valueExpr" type="javascript">row["BRO_DESCR"] != null</expression>
                            </structure>
                        </list-property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">BRO_DESCR</property>
                                <text-property name="displayName">BRO_DESCR</text-property>
                                <expression name="expression" type="javascript">dataSetRow["BRO_DESCR"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[No broker]]></text-property>
                    </text>
                    <data id="339">
                        <property name="fontSize">10pt</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">BRO_DESCR</property>
                                <text-property name="displayName">BRO_DESCR</text-property>
                                <expression name="expression" type="javascript">dataSetRow["BRO_DESCR"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">BRO_DESCR</property>
                    </data>
                </cell>
            </row>
        </grid>
        <text id="589">
            <property name="contentType">auto</property>
            <text-property name="content"><![CDATA[ ]]></text-property>
        </text>
        <table name="Settlements Schedule" id="529">
            <property name="dataSet">Data Set 2</property>
            <list-property name="boundDataColumns">
                <structure>
                    <property name="name">TS_STARTTIME</property>
                    <text-property name="displayName">TS_STARTTIME</text-property>
                    <expression name="expression" type="javascript">dataSetRow["TS_STARTTIME"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">TS_ENDTIME</property>
                    <text-property name="displayName">TS_ENDTIME</text-property>
                    <expression name="expression" type="javascript">dataSetRow["TS_ENDTIME"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">DELDAYS</property>
                    <text-property name="displayName">DELDAYS</text-property>
                    <expression name="expression" type="javascript">dataSetRow["DELDAYS"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">TIMES_</property>
                    <text-property name="displayName">TIMES_</text-property>
                    <expression name="expression" type="javascript">dataSetRow["TIMES_"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">QTY</property>
                    <text-property name="displayName">QTY</text-property>
                    <expression name="expression" type="javascript">dataSetRow["QTY"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">AMT_UNIT</property>
                    <text-property name="displayName">AMT_UNIT</text-property>
                    <expression name="expression" type="javascript">dataSetRow["AMT_UNIT"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">AMT_PERIOD</property>
                    <text-property name="displayName">AMT_PERIOD</text-property>
                    <expression name="expression" type="javascript">dataSetRow["AMT_PERIOD"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">PRICE_TRD</property>
                    <text-property name="displayName">PRICE_TRD</text-property>
                    <expression name="expression" type="javascript">dataSetRow["PRICE_TRD"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">FLOAT_MULT</property>
                    <text-property name="displayName">FLOAT_MULT</text-property>
                    <expression name="expression" type="javascript">dataSetRow["FLOAT_MULT"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
            </list-property>
            <column id="560"/>
            <column id="561"/>
            <column id="562"/>
            <column id="563"/>
            <column id="564"/>
            <column id="565"/>
            <column id="566"/>
            <column id="567"/>
            <column id="568"/>
            <header>
                <row id="530">
                    <cell id="531">
                        <property name="textAlign">left</property>
                        <text id="227">
                            <property name="fontSize">10pt</property>
                            <property name="textAlign">left</property>
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[Start Date]]></text-property>
                        </text>
                    </cell>
                    <cell id="532">
                        <property name="textAlign">left</property>
                        <text id="228">
                            <property name="fontSize">10pt</property>
                            <property name="textAlign">left</property>
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[End Date]]></text-property>
                        </text>
                    </cell>
                    <cell id="533">
                        <property name="textAlign">left</property>
                        <text id="229">
                            <property name="fontSize">10pt</property>
                            <property name="textAlign">left</property>
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[Days]]></text-property>
                        </text>
                    </cell>
                    <cell id="534">
                        <property name="textAlign">left</property>
                        <text id="230">
                            <property name="fontSize">10pt</property>
                            <property name="textAlign">left</property>
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[Hours Ending]]></text-property>
                        </text>
                    </cell>
                    <cell id="535">
                        <property name="textAlign">left</property>
                        <text id="231">
                            <property name="fontSize">10pt</property>
                            <property name="textAlign">left</property>
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[Amt/Per]]></text-property>
                        </text>
                    </cell>
                    <cell id="536">
                        <property name="textAlign">left</property>
                        <text id="232">
                            <property name="fontSize">10pt</property>
                            <property name="textAlign">left</property>
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[Unit]]></text-property>
                        </text>
                    </cell>
                    <cell id="537">
                        <property name="textAlign">left</property>
                        <text id="233">
                            <property name="fontSize">10pt</property>
                            <property name="textAlign">left</property>
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[Period]]></text-property>
                        </text>
                    </cell>
                    <cell id="538">
                        <property name="textAlign">left</property>
                        <text id="234">
                            <property name="fontSize">10pt</property>
                            <property name="textAlign">left</property>
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[$/Unit]]></text-property>
                        </text>
                    </cell>
                    <cell id="539">
                        <property name="textAlign">left</property>
                        <text id="235">
                            <property name="fontSize">10pt</property>
                            <property name="textAlign">left</property>
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[Heat Rate]]></text-property>
                        </text>
                    </cell>
                </row>
            </header>
            <detail>
                <row id="540">
                    <property name="height">24pt</property>
                    <cell id="541">
                        <property name="textAlign">left</property>
                        <data id="663">
                            <property name="resultSetColumn">TS_STARTTIME</property>
                        </data>
                    </cell>
                    <cell id="542">
                        <property name="textAlign">left</property>
                        <data id="664">
                            <property name="resultSetColumn">TS_ENDTIME</property>
                        </data>
                    </cell>
                    <cell id="543">
                        <property name="textAlign">left</property>
                        <data id="665">
                            <property name="resultSetColumn">DELDAYS</property>
                        </data>
                    </cell>
                    <cell id="544">
                        <property name="textAlign">left</property>
                        <data id="666">
                            <property name="resultSetColumn">TIMES_</property>
                        </data>
                    </cell>
                    <cell id="545">
                        <property name="textAlign">left</property>
                        <data id="667">
                            <property name="resultSetColumn">QTY</property>
                        </data>
                    </cell>
                    <cell id="546">
                        <property name="textAlign">left</property>
                        <data id="668">
                            <property name="resultSetColumn">AMT_UNIT</property>
                        </data>
                    </cell>
                    <cell id="547">
                        <property name="textAlign">left</property>
                        <data id="669">
                            <property name="resultSetColumn">AMT_PERIOD</property>
                        </data>
                    </cell>
                    <cell id="548">
                        <property name="textAlign">left</property>
                        <data id="670">
                            <property name="resultSetColumn">PRICE_TRD</property>
                        </data>
                    </cell>
                    <cell id="549">
                        <property name="textAlign">left</property>
                        <data id="671">
                            <property name="resultSetColumn">FLOAT_MULT</property>
                        </data>
                    </cell>
                </row>
            </detail>
        </table>
        <text id="591">
            <property name="contentType">auto</property>
            <text-property name="content"><![CDATA[ ]]></text-property>
        </text>
        <grid name="Execution Clause" id="620">
            <column id="621"/>
            <row id="622">
                <cell id="623">
                    <text-data name="Execution Clause 1" id="597">
                        <property name="display">inline</property>
                        <expression name="valueExpr">"Please confirm that the terms stated herein accurately reflect your understanding of the transaction reached on  "</expression>
                        <property name="contentType">html</property>
                    </text-data>
                    <text id="728">
                        <property name="backgroundColor">#FFFFFF</property>
                        <property name="display">inline</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[ ]]></text-property>
                    </text>
                    <data name="Execution Clause 2" id="600">
                        <structure name="dateTimeFormat">
                            <property name="category">Long Date</property>
                            <property name="pattern">Long Date</property>
                        </structure>
                        <property name="display">inline</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">TRADE_DATE</property>
                                <text-property name="displayName">TRADE_DATE</text-property>
                                <expression name="expression" type="javascript">dataSetRow["TRADE_DATE"]</expression>
                                <property name="dataType">date-time</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">TRADE_DATE</property>
                    </data>
                    <text id="729">
                        <property name="backgroundColor">#FFFFFF</property>
                        <property name="display">inline</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[ ]]></text-property>
                    </text>
                    <text-data name="Execution Clause 3" id="598">
                        <property name="display">inline</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">MA_ATTR1</property>
                                <text-property name="displayName">MA_ATTR1</text-property>
                                <expression name="expression" type="javascript">dataSetRow["MA_ATTR1"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <expression name="valueExpr">" between Counterparty and " + row["MA_ATTR1"] + " by returning an executed copy of this Confirmation to " + row["MA_ATTR1"] + "."</expression>
                        <property name="contentType">html</property>
                    </text-data>
                    <text id="601">
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[ ]]></text-property>
                    </text>
                    <text id="578">
                        <property name="fontSize">10pt</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[If you do not agree with the commercial terms of this Transaction, please contact <NAME_EMAIL> <mailto:<EMAIL>>, via facsimile at ************, or contact Erin Root at ************.]]></text-property>
                    </text>
                </cell>
            </row>
        </grid>
        <text id="592">
            <property name="contentType">auto</property>
            <text-property name="content"><![CDATA[ ]]></text-property>
        </text>
        <grid name="Signature" id="638">
            <column id="639"/>
            <row id="640">
                <cell id="641">
                    <data id="341">
                        <property name="fontSize">10pt</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">CPTY_DESCR</property>
                                <text-property name="displayName">CPTY_DESCR</text-property>
                                <expression name="expression" type="javascript">dataSetRow["CPTY_DESCR"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">CPTY_DESCR</property>
                    </data>
                    <text id="636">
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[ ]]></text-property>
                    </text>
                    <text name="Signature 1" id="239">
                        <property name="fontSize">10pt</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[By:     __________________]]></text-property>
                    </text>
                    <text name="Signature 2" id="240">
                        <property name="fontSize">10pt</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[Title:   __________________]]></text-property>
                    </text>
                    <text name="Signature 3" id="241">
                        <property name="fontSize">10pt</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[Date:  __________________]]></text-property>
                    </text>
                </cell>
            </row>
        </grid>
        <text id="637">
            <property name="contentType">auto</property>
            <text-property name="content"><![CDATA[ ]]></text-property>
        </text>
        <grid name="Attachment" id="648">
            <column id="649"/>
            <row id="650">
                <cell id="651">
                    <text name="Attachment Title" id="243">
                        <property name="fontSize">10pt</property>
                        <property name="textUnderline">underline</property>
                        <property name="textAlign">center</property>
                        <property name="pageBreakBefore">always</property>
                        <property name="contentType">plain</property>
                        <text-property name="content"><![CDATA[Attachment A]]></text-property>
                    </text>
                    <text name="Attachment Description" id="527">
                        <property name="fontSize">10pt</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[This Transaction Confirmation is governed by the Western Systems Power Pool Agreement published and in effect as of the Trade Date hereof ("WSPP Agreement") and is subject to all the terms and provisions of such agreement, as modified by this Attachment A. In the event of a conflict between the provisions of the WSPP Agreement and this Transaction Confirmation, this Transaction Confirmation shall control. The WSPP Agreement, together with the schedules and any written supplements thereto, this Transaction Confirmation and all other Transaction Confirmations in effect between the Parties under the WSPP Agreement shall form a single integrated agreement between the Parties, referred to below as the "Agreement."

The following definitions shall be added to Section 4, DEFINITIONS, of the WSPP Agreement:
"DRUC Schedule Deadline" means ERCOT's Day-Ahead Reliability Unit Commitment deadline for each day that Product is to be delivered under the Transaction, as defined in the ERCOT Nodal Protocols.

"HRUC Schedule Deadline" means ERCOT's Hourly Reliability Unit Commitment deadline for each hour that Product is to be delivered under the Transaction, as defined in the ERCOT Nodal Protocols.

The following provisions shall be added at the end of Section 21.3(a)(1), LIABILITIES AND DAMAGES, of the WSPP Agreement to deal with Purchaser's failure with respect to Firm (LD) Transactions and Firm (No Force Majeure) Transactions with Delivery Points in ERCOT:
"If the Delivery Point is in ERCOT then, in addition to the amounts set forth above, Purchaser shall pay to Seller an amount equal to the ERCOT charges incurred by Seller, if any, as a result of Purchaser's failure to schedule all or a portion of the Contract Quantity prior to any applicable DRUC Schedule Deadline or HRUC Schedule Deadline. The invoice for such amount shall include a written statement explaining in reasonable detail the calculation of such amount."

The following provisions shall be added at the end of Section 21.3(a)(2), LIABILITIES AND DAMAGES, of the WSPP Agreement to deal with Seller's failure with respect to Firm (LD) Transactions and Firm (No Force Majeure) Transactions with Delivery Points in ERCOT:
"If the Delivery Point is in ERCOT, then in addition to the amounts set forth above, an amount equal to the ERCOT charges incurred by Purchaser, if any, as a result of Seller's failure to schedule all or a portion of the Contract Quantity prior to any applicable DRUC Schedule Deadline or HRUC Schedule Deadline. The invoice for such amount shall include a written statement explaining in reasonable detail the calculation of such amount."

The following events shall be added to Section 22.1, EVENTS OF DEFAULT, of the WSPP Agreement:
(f) The failure of the Defaulting Party to pay its debts generally as they become due or the Defaulting Party's admission in writing that it is unable to generally pay its debts as they become due;
(g) The institution, by the Defaulting Party, of a general assignment for the benefit of its creditors; or
(h) The application for, consent to, or acquiescence to, by the Defaulting Party, the appointment of a receiver, custodian, trustee, liquidator, or similar official for all or a substantial portion of its assets."

Section 24, GOVERNING LAW, of the WSPP Agreement is deleted and replaced in its entirety with the following:
"This Agreement (as well as any claim or controversy arising out of or relating to this Agreement) shall be governed by and construed in accordance with the laws of the State of New York, without regard to the conflicts of laws rules thereof other than as set forth in N.Y. General Obligations Law §5-1401."

The netting provisions of Section 28, NETTING, of the WSPP Agreement shall apply to the transaction covered by this Transaction Confirmation as if Buyer and Seller had both executed Exhibit A, NETTING, to the WSPP Agreement.

Section 34, DISPUTE RESOLUTION, of the WSPP Agreement is hereby deleted in its entirety; the provisions of Section 34 are replaced with the following:

"34. Any controversy or claim arising out of or relating to this Agreement, or the breach thereof, shall be settled by an arbitration conducted in accordance with the Commercial Arbitration Rules ("Rules") of the American Arbitration Association ("AAA"), and judgment on the award rendered by the arbitrators may be entered and enforced by any court having jurisdiction thereof. Each Party shall appoint one (1) arbitrator in accordance with said Rules and those two (2) arbitrators shall appoint the third arbitrator, within twenty (20) days, to preside over the tribunal. If the two (2) arbitrators are not able to agree upon a third arbitrator, the third arbitrator shall be appointed by the AAA in accordance with its Rules. The arbitration proceeding shall be held in the State of New York. In construing this Agreement, the Arbitrators shall apply the laws of the State of New York. The arbitrators may award damages but shall have no power to amend or add to this Agreement. Subject to such limitation, the decision of the arbitrators shall be final and binding on the Parties. The decision of the arbitrators shall determine and specify how the expenses of the arbitration shall be allocated between the Parties. In the event either Party must file a court action to enforce an arbitration award under this Section, the prevailing Party shall be entitled to recover its court costs and reasonable attorney fees. EACH PARTY IRREVOCABLY WAIVES ANY AND ALL ITS RESPECTIVE RIGHTS TO ANY JURY TRIAL WITH RESPECT TO ANY LITIGATION OR JUDICIAL PROCEEDINGS ARISING UNDER OR IN CONNECTION WITH THIS AGREEMENT."

The existence, contents or results of any arbitration hereunder may not be disclosed without the prior written consent of both Parties. The provisions of Exhibit D "WSPP Mediation And Arbitration Procedures" are replaced with the following: "EXHIBIT D [Reserved]."

The WSPP Agreement is hereby amended to include the following agreement between the Parties:
(a) Absent the agreement of all Parties to the proposed change, the standard of review for changes to any rate, charge, classification, term or condition of this Agreement proposed by a Party (to the extent that any waiver in subsection (b) below is unenforceable or ineffective as to such Party), by FERC acting sua sponte or by non-parties will be the "public interest" application of the "just and reasonable" standard of review set forth in United Gas Pipe Line Co. v. Mobile Gas Service Corp., 350 U.S. 332 (1956) and Federal Power Commission v. Sierra Pacific Power Co., 350 U.S. 348 (1956) and clarified by NRG Power Marketing LLC v. Maine Pub. Util. Comm'n, 558 U.S. __ (2010) (commonly known as the "Mobile-Sierra doctrine").

(b) In addition, and notwithstanding the foregoing subsection (a), to the fullest extent permitted by applicable law, each Party, for itself and its successors and assigns, hereby expressly and irrevocably waives any rights it can or may have, now or in the future, whether under §§ 205 and/or 206 of the Federal Power Act or otherwise, to seek to obtain from FERC by any means, directly or indirectly (through complaint, investigation or otherwise), and each hereby covenants and agrees not at any time to seek to so obtain, an order from FERC changing any section of this Agreement specifying the rate, charge, classification, or other term or condition agreed to by the Parties, it being the express intent of the Parties that, to the fullest extent permitted by applicable law, neither Party shall unilaterally seek to obtain from FERC any relief changing the rate, charge, classification, or other term or condition of this Agreement, notwithstanding any subsequent changes in applicable law or market conditions that may occur."

With respect to transactions with a Delivery Point(s) in ERCOT covering a Delivery Period that includes a period of time in which the zonal market is effective and extends beyond the Texas Nodal Market Implementation Date, the Parties agree that Purchaser and Seller shall schedule each hour's deliveries with ERCOT prior to that hour's HRUC Schedule Deadline. In addition, Purchaser and Seller shall schedule each day's deliveries with ERCOT prior to that day's DRUC Schedule Deadline if the Transaction was entered into prior to that day's DRUC Schedule Deadline.
]]></text-property>
                    </text>
                </cell>
            </row>
        </grid>
    </body>
</report>
