<?xml version="1.0" encoding="UTF-8"?>
<report xmlns="http://www.eclipse.org/birt/2005/design" version="3.2.26" id="1">
    <property name="createdBy">Eclipse BIRT Designer Version 4.17.0.v202409011308</property>
    <property name="units">in</property>
    <property name="iconFile">/templates/blank_report.gif</property>
    <property name="bidiLayoutOrientation">ltr</property>
    <property name="imageDPI">96</property>
    <parameters>
        <scalar-parameter name="TNUM" id="23">
            <text-property name="promptText">Enter a ticket number:</text-property>
            <property name="valueType">static</property>
            <property name="isRequired">true</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="concealValue">false</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
    </parameters>
    <data-sources>
        <oda-data-source extensionID="org.eclipse.birt.report.data.oda.jdbc" name="Data Source" id="24">
            <list-property name="privateDriverProperties">
                <ex-property>
                    <name>contentBidiFormatStr</name>
                    <value>ILYNN</value>
                </ex-property>
                <ex-property>
                    <name>disabledContentBidiFormatStr</name>
                </ex-property>
                <ex-property>
                    <name>disabledMetadataBidiFormatStr</name>
                </ex-property>
                <ex-property>
                    <name>metadataBidiFormatStr</name>
                    <value>ILYNN</value>
                </ex-property>
            </list-property>
            <property name="odaDriverClass">oracle.jdbc.driver.OracleDriver</property>
            <property name="odaURL">***************************************</property>
            <property name="odaUser">system</property>
            <encrypted-property name="odaPassword" encryptionID="base64">VWx0cmFvbmUyMDI0</encrypted-property>
        </oda-data-source>
    </data-sources>
    <data-sets>
        <oda-data-set extensionID="org.eclipse.birt.report.data.oda.jdbc.JdbcSelectDataSet" name="Data Set 1" id="399">
            <list-property name="columnHints">
                <structure>
                    <property name="columnName">B_S</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">B_S</text-property>
                    <text-property name="heading">B_S</text-property>
                </structure>
                <structure>
                    <property name="columnName">CDY1_ATTR1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CDY1_ATTR1</text-property>
                    <text-property name="heading">CDY1_ATTR1</text-property>
                </structure>
                <structure>
                    <property name="columnName">CDYDESC1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CDYDESC1</text-property>
                    <text-property name="heading">CDYDESC1</text-property>
                </structure>
                <structure>
                    <property name="columnName">CDY1_RISKDESC</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CDY1_RISKDESC</text-property>
                    <text-property name="heading">CDY1_RISKDESC</text-property>
                </structure>
                <structure>
                    <property name="columnName">FIX_METHD1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">FIX_METHD1</text-property>
                    <text-property name="heading">FIX_METHD1</text-property>
                </structure>
                <structure>
                    <property name="columnName">MKT1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">MKT1</text-property>
                    <text-property name="heading">MKT1</text-property>
                </structure>
                <structure>
                    <property name="columnName">PSET1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PSET1</text-property>
                    <text-property name="heading">PSET1</text-property>
                </structure>
                <structure>
                    <property name="columnName">AMT_PERIOD</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">AMT_PERIOD</text-property>
                    <text-property name="heading">AMT_PERIOD</text-property>
                </structure>
                <structure>
                    <property name="columnName">ZKEY</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">ZKEY</text-property>
                    <text-property name="heading">ZKEY</text-property>
                </structure>
                <structure>
                    <property name="columnName">NUMBLKS</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">NUMBLKS</text-property>
                    <text-property name="heading">NUMBLKS</text-property>
                </structure>
                <structure>
                    <property name="columnName">BRO_DESCR</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">BRO_DESCR</text-property>
                    <text-property name="heading">BRO_DESCR</text-property>
                </structure>
                <structure>
                    <property name="columnName">BROKER</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">BROKER</text-property>
                    <text-property name="heading">BROKER</text-property>
                </structure>
                <structure>
                    <property name="columnName">CDYDESC1_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CDYDESC1_</text-property>
                    <text-property name="heading">CDYDESC1_</text-property>
                </structure>
                <structure>
                    <property name="columnName">CDYDESC2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CDYDESC2</text-property>
                    <text-property name="heading">CDYDESC2</text-property>
                </structure>
                <structure>
                    <property name="columnName">CDY1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CDY1</text-property>
                    <text-property name="heading">CDY1</text-property>
                </structure>
                <structure>
                    <property name="columnName">CDYDESC2_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CDYDESC2_</text-property>
                    <text-property name="heading">CDYDESC2_</text-property>
                </structure>
                <structure>
                    <property name="columnName">CDY1_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CDY1_</text-property>
                    <text-property name="heading">CDY1_</text-property>
                </structure>
                <structure>
                    <property name="columnName">CDY2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CDY2</text-property>
                    <text-property name="heading">CDY2</text-property>
                </structure>
                <structure>
                    <property name="columnName">CPTY</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CPTY</text-property>
                    <text-property name="heading">CPTY</text-property>
                </structure>
                <structure>
                    <property name="columnName">CPTY_ADDR1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CPTY_ADDR1</text-property>
                    <text-property name="heading">CPTY_ADDR1</text-property>
                </structure>
                <structure>
                    <property name="columnName">CPTY_ADDR2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CPTY_ADDR2</text-property>
                    <text-property name="heading">CPTY_ADDR2</text-property>
                </structure>
                <structure>
                    <property name="columnName">CPTY_ADDR3</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CPTY_ADDR3</text-property>
                    <text-property name="heading">CPTY_ADDR3</text-property>
                </structure>
                <structure>
                    <property name="columnName">CPTY_ADDR4</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CPTY_ADDR4</text-property>
                    <text-property name="heading">CPTY_ADDR4</text-property>
                </structure>
                <structure>
                    <property name="columnName">CPTY_DESCR</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CPTY_DESCR</text-property>
                    <text-property name="heading">CPTY_DESCR</text-property>
                </structure>
                <structure>
                    <property name="columnName">CPTY_FAX</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CPTY_FAX</text-property>
                    <text-property name="heading">CPTY_FAX</text-property>
                </structure>
                <structure>
                    <property name="columnName">CPTY_PHONE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CPTY_PHONE</text-property>
                    <text-property name="heading">CPTY_PHONE</text-property>
                </structure>
                <structure>
                    <property name="columnName">CPTY_EMAIL</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CPTY_EMAIL</text-property>
                    <text-property name="heading">CPTY_EMAIL</text-property>
                </structure>
                <structure>
                    <property name="columnName">DELDATEEND_STR</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">DELDATEEND_STR</text-property>
                    <text-property name="heading">DELDATEEND_STR</text-property>
                </structure>
                <structure>
                    <property name="columnName">DELDATESTART_STR</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">DELDATESTART_STR</text-property>
                    <text-property name="heading">DELDATESTART_STR</text-property>
                </structure>
                <structure>
                    <property name="columnName">DELDATEEND_STR_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">DELDATEEND_STR_</text-property>
                    <text-property name="heading">DELDATEEND_STR_</text-property>
                </structure>
                <structure>
                    <property name="columnName">FIRMNESS</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">FIRMNESS</text-property>
                    <text-property name="heading">FIRMNESS</text-property>
                </structure>
                <structure>
                    <property name="columnName">FIX_METHD2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">FIX_METHD2</text-property>
                    <text-property name="heading">FIX_METHD2</text-property>
                </structure>
                <structure>
                    <property name="columnName">FIX_METHD</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">FIX_METHD</text-property>
                    <text-property name="heading">FIX_METHD</text-property>
                </structure>
                <structure>
                    <property name="columnName">FLOAT_MULT</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">FLOAT_MULT</text-property>
                    <text-property name="heading">FLOAT_MULT</text-property>
                </structure>
                <structure>
                    <property name="columnName">FLOAT_MULT_</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">FLOAT_MULT_</text-property>
                    <text-property name="heading">FLOAT_MULT_</text-property>
                </structure>
                <structure>
                    <property name="columnName">FLOAT_MULT__</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">FLOAT_MULT__</text-property>
                    <text-property name="heading">FLOAT_MULT__</text-property>
                </structure>
                <structure>
                    <property name="columnName">HOURALL</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">HOURALL</text-property>
                    <text-property name="heading">HOURALL</text-property>
                </structure>
                <structure>
                    <property name="columnName">HOUSE_ADDR1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">HOUSE_ADDR1</text-property>
                    <text-property name="heading">HOUSE_ADDR1</text-property>
                </structure>
                <structure>
                    <property name="columnName">HOUSE_ADDR2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">HOUSE_ADDR2</text-property>
                    <text-property name="heading">HOUSE_ADDR2</text-property>
                </structure>
                <structure>
                    <property name="columnName">HOUSE_ADDR4</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">HOUSE_ADDR4</text-property>
                    <text-property name="heading">HOUSE_ADDR4</text-property>
                </structure>
                <structure>
                    <property name="columnName">HOUSEFULLNAME</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">HOUSEFULLNAME</text-property>
                    <text-property name="heading">HOUSEFULLNAME</text-property>
                </structure>
                <structure>
                    <property name="columnName">HOUSEFULLNAME_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">HOUSEFULLNAME_</text-property>
                    <text-property name="heading">HOUSEFULLNAME_</text-property>
                </structure>
                <structure>
                    <property name="columnName">HOUSE_ID</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">HOUSE_ID</text-property>
                    <text-property name="heading">HOUSE_ID</text-property>
                </structure>
                <structure>
                    <property name="columnName">MA_ATTR1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">MA_ATTR1</text-property>
                    <text-property name="heading">MA_ATTR1</text-property>
                </structure>
                <structure>
                    <property name="columnName">MA_CON_TYPE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">MA_CON_TYPE</text-property>
                    <text-property name="heading">MA_CON_TYPE</text-property>
                </structure>
                <structure>
                    <property name="columnName">MA_DESCRIPTION</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">MA_DESCRIPTION</text-property>
                    <text-property name="heading">MA_DESCRIPTION</text-property>
                </structure>
                <structure>
                    <property name="columnName">MA_EFFECTIVE_DT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">MA_EFFECTIVE_DT</text-property>
                    <text-property name="heading">MA_EFFECTIVE_DT</text-property>
                </structure>
                <structure>
                    <property name="columnName">MA_EXPIRATION_DT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">MA_EXPIRATION_DT</text-property>
                    <text-property name="heading">MA_EXPIRATION_DT</text-property>
                </structure>
                <structure>
                    <property name="columnName">MANUM</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">MANUM</text-property>
                    <text-property name="heading">MANUM</text-property>
                </structure>
                <structure>
                    <property name="columnName">MKT1_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">MKT1_</text-property>
                    <text-property name="heading">MKT1_</text-property>
                </structure>
                <structure>
                    <property name="columnName">MKT2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">MKT2</text-property>
                    <text-property name="heading">MKT2</text-property>
                </structure>
                <structure>
                    <property name="columnName">OPTMODEL</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">OPTMODEL</text-property>
                    <text-property name="heading">OPTMODEL</text-property>
                </structure>
                <structure>
                    <property name="columnName">OPT_TYPE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">OPT_TYPE</text-property>
                    <text-property name="heading">OPT_TYPE</text-property>
                </structure>
                <structure>
                    <property name="columnName">PHYS_BUYER2_ALT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PHYS_BUYER2_ALT</text-property>
                    <text-property name="heading">PHYS_BUYER2_ALT</text-property>
                </structure>
                <structure>
                    <property name="columnName">PHYS_BUYER2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PHYS_BUYER2</text-property>
                    <text-property name="heading">PHYS_BUYER2</text-property>
                </structure>
                <structure>
                    <property name="columnName">PHYS_SELLER2_ALT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PHYS_SELLER2_ALT</text-property>
                    <text-property name="heading">PHYS_SELLER2_ALT</text-property>
                </structure>
                <structure>
                    <property name="columnName">PHYS_SELLER2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PHYS_SELLER2</text-property>
                    <text-property name="heading">PHYS_SELLER2</text-property>
                </structure>
                <structure>
                    <property name="columnName">CDY1_POWER_REGION</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CDY1_POWER_REGION</text-property>
                    <text-property name="heading">CDY1_POWER_REGION</text-property>
                </structure>
                <structure>
                    <property name="columnName">PRC_PERIOD</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PRC_PERIOD</text-property>
                    <text-property name="heading">PRC_PERIOD</text-property>
                </structure>
                <structure>
                    <property name="columnName">PRC_UNIT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PRC_UNIT</text-property>
                    <text-property name="heading">PRC_UNIT</text-property>
                </structure>
                <structure>
                    <property name="columnName">PREM_DATE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PREM_DATE</text-property>
                    <text-property name="heading">PREM_DATE</text-property>
                </structure>
                <structure>
                    <property name="columnName">PREM_PERIOD</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PREM_PERIOD</text-property>
                    <text-property name="heading">PREM_PERIOD</text-property>
                </structure>
                <structure>
                    <property name="columnName">PREM_RATE</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">PREM_RATE</text-property>
                    <text-property name="heading">PREM_RATE</text-property>
                </structure>
                <structure>
                    <property name="columnName">PREM_UNIT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PREM_UNIT</text-property>
                    <text-property name="heading">PREM_UNIT</text-property>
                </structure>
                <structure>
                    <property name="columnName">PREMIUM</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">PREMIUM</text-property>
                    <text-property name="heading">PREMIUM</text-property>
                </structure>
                <structure>
                    <property name="columnName">STRIKE</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">STRIKE</text-property>
                    <text-property name="heading">STRIKE</text-property>
                </structure>
                <structure>
                    <property name="columnName">PRC_PERIOD_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PRC_PERIOD_</text-property>
                    <text-property name="heading">PRC_PERIOD_</text-property>
                </structure>
                <structure>
                    <property name="columnName">PRICE_STRIKE</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">PRICE_STRIKE</text-property>
                    <text-property name="heading">PRICE_STRIKE</text-property>
                </structure>
                <structure>
                    <property name="columnName">PRC_UNIT_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PRC_UNIT_</text-property>
                    <text-property name="heading">PRC_UNIT_</text-property>
                </structure>
                <structure>
                    <property name="columnName">PSET1_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PSET1_</text-property>
                    <text-property name="heading">PSET1_</text-property>
                </structure>
                <structure>
                    <property name="columnName">PSET2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PSET2</text-property>
                    <text-property name="heading">PSET2</text-property>
                </structure>
                <structure>
                    <property name="columnName">PSET_DESC1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PSET_DESC1</text-property>
                    <text-property name="heading">PSET_DESC1</text-property>
                </structure>
                <structure>
                    <property name="columnName">PUTCALL</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PUTCALL</text-property>
                    <text-property name="heading">PUTCALL</text-property>
                </structure>
                <structure>
                    <property name="columnName">AMT_PERIOD_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">AMT_PERIOD_</text-property>
                    <text-property name="heading">AMT_PERIOD_</text-property>
                </structure>
                <structure>
                    <property name="columnName">UOM</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">UOM</text-property>
                    <text-property name="heading">UOM</text-property>
                </structure>
                <structure>
                    <property name="columnName">REPORT_DATE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">REPORT_DATE</text-property>
                    <text-property name="heading">REPORT_DATE</text-property>
                </structure>
                <structure>
                    <property name="columnName">DELDATESTART_STR_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">DELDATESTART_STR_</text-property>
                    <text-property name="heading">DELDATESTART_STR_</text-property>
                </structure>
                <structure>
                    <property name="columnName">DELDATESTART_STR__</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">DELDATESTART_STR__</text-property>
                    <text-property name="heading">DELDATESTART_STR__</text-property>
                </structure>
                <structure>
                    <property name="columnName">PRICE_STRIKE_</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">PRICE_STRIKE_</text-property>
                    <text-property name="heading">PRICE_STRIKE_</text-property>
                </structure>
                <structure>
                    <property name="columnName">TNUM</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TNUM</text-property>
                    <text-property name="heading">TNUM</text-property>
                </structure>
                <structure>
                    <property name="columnName">TRADE_DATE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TRADE_DATE</text-property>
                    <text-property name="heading">TRADE_DATE</text-property>
                </structure>
                <structure>
                    <property name="columnName">TRADE_TEMPLATE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TRADE_TEMPLATE</text-property>
                    <text-property name="heading">TRADE_TEMPLATE</text-property>
                </structure>
                <structure>
                    <property name="columnName">TRADE_TEMPLATE_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TRADE_TEMPLATE_</text-property>
                    <text-property name="heading">TRADE_TEMPLATE_</text-property>
                </structure>
                <structure>
                    <property name="columnName">TRADE_TYPE2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TRADE_TYPE2</text-property>
                    <text-property name="heading">TRADE_TYPE2</text-property>
                </structure>
                <structure>
                    <property name="columnName">ZKEY_</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">ZKEY_</text-property>
                    <text-property name="heading">ZKEY_</text-property>
                </structure>
                <structure>
                    <property name="columnName">LOGONAME</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">LOGONAME</text-property>
                    <text-property name="heading">LOGONAME</text-property>
                </structure>
                <structure>
                    <property name="columnName">ERCOTDESC</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">ERCOTDESC</text-property>
                    <text-property name="heading">ERCOTDESC</text-property>
                </structure>
                <structure>
                    <property name="columnName">CALCAGENT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CALCAGENT</text-property>
                    <text-property name="heading">CALCAGENT</text-property>
                </structure>
                <structure>
                    <property name="columnName">FIXPAYOR</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">FIXPAYOR</text-property>
                    <text-property name="heading">FIXPAYOR</text-property>
                </structure>
                <structure>
                    <property name="columnName">FLOATPAYOR</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">FLOATPAYOR</text-property>
                    <text-property name="heading">FLOATPAYOR</text-property>
                </structure>
                <structure>
                    <property name="columnName">CAPCITYPWR</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CAPCITYPWR</text-property>
                    <text-property name="heading">CAPCITYPWR</text-property>
                </structure>
                <structure>
                    <property name="columnName">CAPCITYPWR2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CAPCITYPWR2</text-property>
                    <text-property name="heading">CAPCITYPWR2</text-property>
                </structure>
                <structure>
                    <property name="columnName">FIRMPROPCASE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">FIRMPROPCASE</text-property>
                    <text-property name="heading">FIRMPROPCASE</text-property>
                </structure>
                <structure>
                    <property name="columnName">SIDE1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SIDE1</text-property>
                    <text-property name="heading">SIDE1</text-property>
                </structure>
                <structure>
                    <property name="columnName">SIDE2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SIDE2</text-property>
                    <text-property name="heading">SIDE2</text-property>
                </structure>
                <structure>
                    <property name="columnName">TRADETYPEINDEX</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TRADETYPEINDEX</text-property>
                    <text-property name="heading">TRADETYPEINDEX</text-property>
                </structure>
                <structure>
                    <property name="columnName">HOUSE_CONTACT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">HOUSE_CONTACT</text-property>
                    <text-property name="heading">HOUSE_CONTACT</text-property>
                </structure>
                <structure>
                    <property name="columnName">CONF__TITLE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CONF__TITLE</text-property>
                    <text-property name="heading">CONF__TITLE</text-property>
                </structure>
                <structure>
                    <property name="columnName">STYLE1C</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">STYLE1C</text-property>
                    <text-property name="heading">STYLE1C</text-property>
                </structure>
                <structure>
                    <property name="columnName">SWAPCLRWCAP</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SWAPCLRWCAP</text-property>
                    <text-property name="heading">SWAPCLRWCAP</text-property>
                </structure>
                <structure>
                    <property name="columnName">HIDEANNEX</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">HIDEANNEX</text-property>
                    <text-property name="heading">HIDEANNEX</text-property>
                </structure>
                <structure>
                    <property name="columnName">BSWAP</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">BSWAP</text-property>
                    <text-property name="heading">BSWAP</text-property>
                </structure>
                <structure>
                    <property name="columnName">HOUSE_TEL</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">HOUSE_TEL</text-property>
                    <text-property name="heading">HOUSE_TEL</text-property>
                </structure>
                <structure>
                    <property name="columnName">RISKDESC</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">RISKDESC</text-property>
                    <text-property name="heading">RISKDESC</text-property>
                </structure>
                <structure>
                    <property name="columnName">PRICE_UOM</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PRICE_UOM</text-property>
                    <text-property name="heading">PRICE_UOM</text-property>
                </structure>
                <structure>
                    <property name="columnName">SHOWSCHEDULE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SHOWSCHEDULE</text-property>
                    <text-property name="heading">SHOWSCHEDULE</text-property>
                </structure>
                <structure>
                    <property name="columnName">SHOWSTRIKEUNIT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SHOWSTRIKEUNIT</text-property>
                    <text-property name="heading">SHOWSTRIKEUNIT</text-property>
                </structure>
                <structure>
                    <property name="columnName">SHOWFLOATING</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SHOWFLOATING</text-property>
                    <text-property name="heading">SHOWFLOATING</text-property>
                </structure>
                <structure>
                    <property name="columnName">PREMIUMGRID</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PREMIUMGRID</text-property>
                    <text-property name="heading">PREMIUMGRID</text-property>
                </structure>
                <structure>
                    <property name="columnName">SHOWDAYCONVENTIONS</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SHOWDAYCONVENTIONS</text-property>
                    <text-property name="heading">SHOWDAYCONVENTIONS</text-property>
                </structure>
                <structure>
                    <property name="columnName">SHOWBUYERSELLER</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SHOWBUYERSELLER</text-property>
                    <text-property name="heading">SHOWBUYERSELLER</text-property>
                </structure>
                <structure>
                    <property name="columnName">SHOWFIRMNESS</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SHOWFIRMNESS</text-property>
                    <text-property name="heading">SHOWFIRMNESS</text-property>
                </structure>
                <structure>
                    <property name="columnName">SHOWPUTCALL</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SHOWPUTCALL</text-property>
                    <text-property name="heading">SHOWPUTCALL</text-property>
                </structure>
                <structure>
                    <property name="columnName">SHOWCOMMODITY</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SHOWCOMMODITY</text-property>
                    <text-property name="heading">SHOWCOMMODITY</text-property>
                </structure>
                <structure>
                    <property name="columnName">DELIVERYTYPE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">DELIVERYTYPE</text-property>
                    <text-property name="heading">DELIVERYTYPE</text-property>
                </structure>
                <structure>
                    <property name="columnName">SHOWCALCAGENT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SHOWCALCAGENT</text-property>
                    <text-property name="heading">SHOWCALCAGENT</text-property>
                </structure>
                <structure>
                    <property name="columnName">SHOWSETTLEMENTPRICE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SHOWSETTLEMENTPRICE</text-property>
                    <text-property name="heading">SHOWSETTLEMENTPRICE</text-property>
                </structure>
            </list-property>
            <list-property name="parameters">
                <structure>
                    <property name="name">param_1</property>
                    <property name="paramName">TNUM</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                    <property name="position">1</property>
                    <property name="isOptional">true</property>
                    <property name="allowNull">true</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
            </list-property>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">B_S</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">2</property>
                        <property name="name">CDY1_ATTR1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">3</property>
                        <property name="name">CDYDESC1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">4</property>
                        <property name="name">CDY1_RISKDESC</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">5</property>
                        <property name="name">FIX_METHD1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">6</property>
                        <property name="name">MKT1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">7</property>
                        <property name="name">PSET1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">8</property>
                        <property name="name">AMT_PERIOD</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">9</property>
                        <property name="name">ZKEY</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">10</property>
                        <property name="name">NUMBLKS</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">11</property>
                        <property name="name">BRO_DESCR</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">12</property>
                        <property name="name">BROKER</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">13</property>
                        <property name="name">CDYDESC1_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">14</property>
                        <property name="name">CDYDESC2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">15</property>
                        <property name="name">CDY1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">16</property>
                        <property name="name">CDYDESC2_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">17</property>
                        <property name="name">CDY1_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">18</property>
                        <property name="name">CDY2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">19</property>
                        <property name="name">CPTY</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">20</property>
                        <property name="name">CPTY_ADDR1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">21</property>
                        <property name="name">CPTY_ADDR2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">22</property>
                        <property name="name">CPTY_ADDR3</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">23</property>
                        <property name="name">CPTY_ADDR4</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">24</property>
                        <property name="name">CPTY_DESCR</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">25</property>
                        <property name="name">CPTY_FAX</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">26</property>
                        <property name="name">CPTY_PHONE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">27</property>
                        <property name="name">CPTY_EMAIL</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">28</property>
                        <property name="name">DELDATEEND_STR</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">29</property>
                        <property name="name">DELDATESTART_STR</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">30</property>
                        <property name="name">DELDATEEND_STR_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">31</property>
                        <property name="name">FIRMNESS</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">32</property>
                        <property name="name">FIX_METHD2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">33</property>
                        <property name="name">FIX_METHD</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">34</property>
                        <property name="name">FLOAT_MULT</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">35</property>
                        <property name="name">FLOAT_MULT_</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">36</property>
                        <property name="name">FLOAT_MULT__</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">37</property>
                        <property name="name">HOURALL</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">38</property>
                        <property name="name">HOUSE_ADDR1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">39</property>
                        <property name="name">HOUSE_ADDR2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">40</property>
                        <property name="name">HOUSE_ADDR4</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">41</property>
                        <property name="name">HOUSEFULLNAME</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">42</property>
                        <property name="name">HOUSEFULLNAME_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">43</property>
                        <property name="name">HOUSE_ID</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">44</property>
                        <property name="name">MA_ATTR1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">45</property>
                        <property name="name">MA_CON_TYPE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">46</property>
                        <property name="name">MA_DESCRIPTION</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">47</property>
                        <property name="name">MA_EFFECTIVE_DT</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">48</property>
                        <property name="name">MA_EXPIRATION_DT</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">49</property>
                        <property name="name">MANUM</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">50</property>
                        <property name="name">MKT1_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">51</property>
                        <property name="name">MKT2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">52</property>
                        <property name="name">OPTMODEL</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">53</property>
                        <property name="name">OPT_TYPE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">54</property>
                        <property name="name">PHYS_BUYER2_ALT</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">55</property>
                        <property name="name">PHYS_BUYER2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">56</property>
                        <property name="name">PHYS_SELLER2_ALT</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">57</property>
                        <property name="name">PHYS_SELLER2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">58</property>
                        <property name="name">CDY1_POWER_REGION</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">59</property>
                        <property name="name">PRC_PERIOD</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">60</property>
                        <property name="name">PRC_UNIT</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">61</property>
                        <property name="name">PREM_DATE</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">62</property>
                        <property name="name">PREM_PERIOD</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">63</property>
                        <property name="name">PREM_RATE</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">64</property>
                        <property name="name">PREM_UNIT</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">65</property>
                        <property name="name">PREMIUM</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">66</property>
                        <property name="name">STRIKE</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">67</property>
                        <property name="name">PRC_PERIOD_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">68</property>
                        <property name="name">PRICE_STRIKE</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">69</property>
                        <property name="name">PRC_UNIT_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">70</property>
                        <property name="name">PSET1_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">71</property>
                        <property name="name">PSET2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">72</property>
                        <property name="name">PSET_DESC1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">73</property>
                        <property name="name">PUTCALL</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">74</property>
                        <property name="name">AMT_PERIOD_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">75</property>
                        <property name="name">UOM</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">76</property>
                        <property name="name">REPORT_DATE</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">77</property>
                        <property name="name">DELDATESTART_STR_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">78</property>
                        <property name="name">DELDATESTART_STR__</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">79</property>
                        <property name="name">PRICE_STRIKE_</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">80</property>
                        <property name="name">TNUM</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">81</property>
                        <property name="name">TRADE_DATE</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">82</property>
                        <property name="name">TRADE_TEMPLATE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">83</property>
                        <property name="name">TRADE_TEMPLATE_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">84</property>
                        <property name="name">TRADE_TYPE2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">85</property>
                        <property name="name">ZKEY_</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">86</property>
                        <property name="name">LOGONAME</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">87</property>
                        <property name="name">ERCOTDESC</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">88</property>
                        <property name="name">CALCAGENT</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">89</property>
                        <property name="name">FIXPAYOR</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">90</property>
                        <property name="name">FLOATPAYOR</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">91</property>
                        <property name="name">CAPCITYPWR</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">92</property>
                        <property name="name">CAPCITYPWR2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">93</property>
                        <property name="name">FIRMPROPCASE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">94</property>
                        <property name="name">SIDE1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">95</property>
                        <property name="name">SIDE2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">96</property>
                        <property name="name">TRADETYPEINDEX</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">97</property>
                        <property name="name">HOUSE_CONTACT</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">98</property>
                        <property name="name">CONF__TITLE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">99</property>
                        <property name="name">STYLE1C</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">100</property>
                        <property name="name">SWAPCLRWCAP</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">101</property>
                        <property name="name">HIDEANNEX</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">102</property>
                        <property name="name">BSWAP</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">103</property>
                        <property name="name">HOUSE_TEL</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">104</property>
                        <property name="name">RISKDESC</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">105</property>
                        <property name="name">PRICE_UOM</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">106</property>
                        <property name="name">SHOWSCHEDULE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">107</property>
                        <property name="name">SHOWSTRIKEUNIT</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">108</property>
                        <property name="name">SHOWFLOATING</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">109</property>
                        <property name="name">PREMIUMGRID</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">110</property>
                        <property name="name">SHOWDAYCONVENTIONS</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">111</property>
                        <property name="name">SHOWBUYERSELLER</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">112</property>
                        <property name="name">SHOWFIRMNESS</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">113</property>
                        <property name="name">SHOWPUTCALL</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">114</property>
                        <property name="name">SHOWCOMMODITY</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">115</property>
                        <property name="name">DELIVERYTYPE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">116</property>
                        <property name="name">SHOWCALCAGENT</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">117</property>
                        <property name="name">SHOWSETTLEMENTPRICE</property>
                        <property name="dataType">string</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">Data Source</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">B_S</property>
                    <property name="nativeName">B_S</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">2</property>
                    <property name="name">CDY1_ATTR1</property>
                    <property name="nativeName">CDY1_ATTR1</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">3</property>
                    <property name="name">CDYDESC1</property>
                    <property name="nativeName">CDYDESC1</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">4</property>
                    <property name="name">CDY1_RISKDESC</property>
                    <property name="nativeName">CDY1_RISKDESC</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">5</property>
                    <property name="name">FIX_METHD1</property>
                    <property name="nativeName">FIX_METHD1</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">6</property>
                    <property name="name">MKT1</property>
                    <property name="nativeName">MKT1</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">7</property>
                    <property name="name">PSET1</property>
                    <property name="nativeName">PSET1</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">8</property>
                    <property name="name">AMT_PERIOD</property>
                    <property name="nativeName">AMT_PERIOD</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">9</property>
                    <property name="name">ZKEY</property>
                    <property name="nativeName">ZKEY</property>
                    <property name="dataType">decimal</property>
                    <property name="nativeDataType">2</property>
                </structure>
                <structure>
                    <property name="position">10</property>
                    <property name="name">NUMBLKS</property>
                    <property name="nativeName">NUMBLKS</property>
                    <property name="dataType">decimal</property>
                    <property name="nativeDataType">2</property>
                </structure>
                <structure>
                    <property name="position">11</property>
                    <property name="name">BRO_DESCR</property>
                    <property name="nativeName">BRO_DESCR</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">12</property>
                    <property name="name">BROKER</property>
                    <property name="nativeName">BROKER</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">13</property>
                    <property name="name">CDYDESC1_</property>
                    <property name="nativeName">CDYDESC1_</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">14</property>
                    <property name="name">CDYDESC2</property>
                    <property name="nativeName">CDYDESC2</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">15</property>
                    <property name="name">CDY1</property>
                    <property name="nativeName">CDY1</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">16</property>
                    <property name="name">CDYDESC2_</property>
                    <property name="nativeName">CDYDESC2_</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">17</property>
                    <property name="name">CDY1_</property>
                    <property name="nativeName">CDY1_</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">18</property>
                    <property name="name">CDY2</property>
                    <property name="nativeName">CDY2</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">19</property>
                    <property name="name">CPTY</property>
                    <property name="nativeName">CPTY</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">20</property>
                    <property name="name">CPTY_ADDR1</property>
                    <property name="nativeName">CPTY_ADDR1</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">21</property>
                    <property name="name">CPTY_ADDR2</property>
                    <property name="nativeName">CPTY_ADDR2</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">22</property>
                    <property name="name">CPTY_ADDR3</property>
                    <property name="nativeName">CPTY_ADDR3</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">23</property>
                    <property name="name">CPTY_ADDR4</property>
                    <property name="nativeName">CPTY_ADDR4</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">24</property>
                    <property name="name">CPTY_DESCR</property>
                    <property name="nativeName">CPTY_DESCR</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">25</property>
                    <property name="name">CPTY_FAX</property>
                    <property name="nativeName">CPTY_FAX</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">26</property>
                    <property name="name">CPTY_PHONE</property>
                    <property name="nativeName">CPTY_PHONE</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">27</property>
                    <property name="name">CPTY_EMAIL</property>
                    <property name="nativeName">CPTY_EMAIL</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">28</property>
                    <property name="name">DELDATEEND_STR</property>
                    <property name="nativeName">DELDATEEND_STR</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">29</property>
                    <property name="name">DELDATESTART_STR</property>
                    <property name="nativeName">DELDATESTART_STR</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">30</property>
                    <property name="name">DELDATEEND_STR_</property>
                    <property name="nativeName">DELDATEEND_STR_</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">31</property>
                    <property name="name">FIRMNESS</property>
                    <property name="nativeName">FIRMNESS</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">32</property>
                    <property name="name">FIX_METHD2</property>
                    <property name="nativeName">FIX_METHD2</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">33</property>
                    <property name="name">FIX_METHD</property>
                    <property name="nativeName">FIX_METHD</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">34</property>
                    <property name="name">FLOAT_MULT</property>
                    <property name="nativeName">FLOAT_MULT</property>
                    <property name="dataType">decimal</property>
                    <property name="nativeDataType">2</property>
                </structure>
                <structure>
                    <property name="position">35</property>
                    <property name="name">FLOAT_MULT_</property>
                    <property name="nativeName">FLOAT_MULT_</property>
                    <property name="dataType">decimal</property>
                    <property name="nativeDataType">2</property>
                </structure>
                <structure>
                    <property name="position">36</property>
                    <property name="name">FLOAT_MULT__</property>
                    <property name="nativeName">FLOAT_MULT__</property>
                    <property name="dataType">decimal</property>
                    <property name="nativeDataType">2</property>
                </structure>
                <structure>
                    <property name="position">37</property>
                    <property name="name">HOURALL</property>
                    <property name="nativeName">HOURALL</property>
                    <property name="dataType">decimal</property>
                    <property name="nativeDataType">2</property>
                </structure>
                <structure>
                    <property name="position">38</property>
                    <property name="name">HOUSE_ADDR1</property>
                    <property name="nativeName">HOUSE_ADDR1</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">39</property>
                    <property name="name">HOUSE_ADDR2</property>
                    <property name="nativeName">HOUSE_ADDR2</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">40</property>
                    <property name="name">HOUSE_ADDR4</property>
                    <property name="nativeName">HOUSE_ADDR4</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">41</property>
                    <property name="name">HOUSEFULLNAME</property>
                    <property name="nativeName">HOUSEFULLNAME</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">42</property>
                    <property name="name">HOUSEFULLNAME_</property>
                    <property name="nativeName">HOUSEFULLNAME_</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">43</property>
                    <property name="name">HOUSE_ID</property>
                    <property name="nativeName">HOUSE_ID</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">44</property>
                    <property name="name">MA_ATTR1</property>
                    <property name="nativeName">MA_ATTR1</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">45</property>
                    <property name="name">MA_CON_TYPE</property>
                    <property name="nativeName">MA_CON_TYPE</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">46</property>
                    <property name="name">MA_DESCRIPTION</property>
                    <property name="nativeName">MA_DESCRIPTION</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">47</property>
                    <property name="name">MA_EFFECTIVE_DT</property>
                    <property name="nativeName">MA_EFFECTIVE_DT</property>
                    <property name="dataType">date-time</property>
                    <property name="nativeDataType">93</property>
                </structure>
                <structure>
                    <property name="position">48</property>
                    <property name="name">MA_EXPIRATION_DT</property>
                    <property name="nativeName">MA_EXPIRATION_DT</property>
                    <property name="dataType">date-time</property>
                    <property name="nativeDataType">93</property>
                </structure>
                <structure>
                    <property name="position">49</property>
                    <property name="name">MANUM</property>
                    <property name="nativeName">MANUM</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">50</property>
                    <property name="name">MKT1_</property>
                    <property name="nativeName">MKT1_</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">51</property>
                    <property name="name">MKT2</property>
                    <property name="nativeName">MKT2</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">52</property>
                    <property name="name">OPTMODEL</property>
                    <property name="nativeName">OPTMODEL</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">53</property>
                    <property name="name">OPT_TYPE</property>
                    <property name="nativeName">OPT_TYPE</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">54</property>
                    <property name="name">PHYS_BUYER2_ALT</property>
                    <property name="nativeName">PHYS_BUYER2_ALT</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">55</property>
                    <property name="name">PHYS_BUYER2</property>
                    <property name="nativeName">PHYS_BUYER2</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">56</property>
                    <property name="name">PHYS_SELLER2_ALT</property>
                    <property name="nativeName">PHYS_SELLER2_ALT</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">57</property>
                    <property name="name">PHYS_SELLER2</property>
                    <property name="nativeName">PHYS_SELLER2</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">58</property>
                    <property name="name">CDY1_POWER_REGION</property>
                    <property name="nativeName">CDY1_POWER_REGION</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">59</property>
                    <property name="name">PRC_PERIOD</property>
                    <property name="nativeName">PRC_PERIOD</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">60</property>
                    <property name="name">PRC_UNIT</property>
                    <property name="nativeName">PRC_UNIT</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">61</property>
                    <property name="name">PREM_DATE</property>
                    <property name="nativeName">PREM_DATE</property>
                    <property name="dataType">date-time</property>
                    <property name="nativeDataType">93</property>
                </structure>
                <structure>
                    <property name="position">62</property>
                    <property name="name">PREM_PERIOD</property>
                    <property name="nativeName">PREM_PERIOD</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">63</property>
                    <property name="name">PREM_RATE</property>
                    <property name="nativeName">PREM_RATE</property>
                    <property name="dataType">decimal</property>
                    <property name="nativeDataType">2</property>
                </structure>
                <structure>
                    <property name="position">64</property>
                    <property name="name">PREM_UNIT</property>
                    <property name="nativeName">PREM_UNIT</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">65</property>
                    <property name="name">PREMIUM</property>
                    <property name="nativeName">PREMIUM</property>
                    <property name="dataType">decimal</property>
                    <property name="nativeDataType">2</property>
                </structure>
                <structure>
                    <property name="position">66</property>
                    <property name="name">STRIKE</property>
                    <property name="nativeName">STRIKE</property>
                    <property name="dataType">decimal</property>
                    <property name="nativeDataType">2</property>
                </structure>
                <structure>
                    <property name="position">67</property>
                    <property name="name">PRC_PERIOD_</property>
                    <property name="nativeName">PRC_PERIOD_</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">68</property>
                    <property name="name">PRICE_STRIKE</property>
                    <property name="nativeName">PRICE_STRIKE</property>
                    <property name="dataType">decimal</property>
                    <property name="nativeDataType">2</property>
                </structure>
                <structure>
                    <property name="position">69</property>
                    <property name="name">PRC_UNIT_</property>
                    <property name="nativeName">PRC_UNIT_</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">70</property>
                    <property name="name">PSET1_</property>
                    <property name="nativeName">PSET1_</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">71</property>
                    <property name="name">PSET2</property>
                    <property name="nativeName">PSET2</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">72</property>
                    <property name="name">PSET_DESC1</property>
                    <property name="nativeName">PSET_DESC1</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">73</property>
                    <property name="name">PUTCALL</property>
                    <property name="nativeName">PUTCALL</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">74</property>
                    <property name="name">AMT_PERIOD_</property>
                    <property name="nativeName">AMT_PERIOD_</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">75</property>
                    <property name="name">UOM</property>
                    <property name="nativeName">UOM</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">76</property>
                    <property name="name">REPORT_DATE</property>
                    <property name="nativeName">REPORT_DATE</property>
                    <property name="dataType">date-time</property>
                    <property name="nativeDataType">93</property>
                </structure>
                <structure>
                    <property name="position">77</property>
                    <property name="name">DELDATESTART_STR_</property>
                    <property name="nativeName">DELDATESTART_STR_</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">78</property>
                    <property name="name">DELDATESTART_STR__</property>
                    <property name="nativeName">DELDATESTART_STR__</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">79</property>
                    <property name="name">PRICE_STRIKE_</property>
                    <property name="nativeName">PRICE_STRIKE_</property>
                    <property name="dataType">decimal</property>
                    <property name="nativeDataType">2</property>
                </structure>
                <structure>
                    <property name="position">80</property>
                    <property name="name">TNUM</property>
                    <property name="nativeName">TNUM</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">81</property>
                    <property name="name">TRADE_DATE</property>
                    <property name="nativeName">TRADE_DATE</property>
                    <property name="dataType">date-time</property>
                    <property name="nativeDataType">93</property>
                </structure>
                <structure>
                    <property name="position">82</property>
                    <property name="name">TRADE_TEMPLATE</property>
                    <property name="nativeName">TRADE_TEMPLATE</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">83</property>
                    <property name="name">TRADE_TEMPLATE_</property>
                    <property name="nativeName">TRADE_TEMPLATE_</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">84</property>
                    <property name="name">TRADE_TYPE2</property>
                    <property name="nativeName">TRADE_TYPE2</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">85</property>
                    <property name="name">ZKEY_</property>
                    <property name="nativeName">ZKEY_</property>
                    <property name="dataType">decimal</property>
                    <property name="nativeDataType">2</property>
                </structure>
                <structure>
                    <property name="position">86</property>
                    <property name="name">LOGONAME</property>
                    <property name="nativeName">LOGONAME</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">87</property>
                    <property name="name">ERCOTDESC</property>
                    <property name="nativeName">ERCOTDESC</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">88</property>
                    <property name="name">CALCAGENT</property>
                    <property name="nativeName">CALCAGENT</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">89</property>
                    <property name="name">FIXPAYOR</property>
                    <property name="nativeName">FIXPAYOR</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">90</property>
                    <property name="name">FLOATPAYOR</property>
                    <property name="nativeName">FLOATPAYOR</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">91</property>
                    <property name="name">CAPCITYPWR</property>
                    <property name="nativeName">CAPCITYPWR</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">92</property>
                    <property name="name">CAPCITYPWR2</property>
                    <property name="nativeName">CAPCITYPWR2</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">93</property>
                    <property name="name">FIRMPROPCASE</property>
                    <property name="nativeName">FIRMPROPCASE</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">94</property>
                    <property name="name">SIDE1</property>
                    <property name="nativeName">SIDE1</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">95</property>
                    <property name="name">SIDE2</property>
                    <property name="nativeName">SIDE2</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">96</property>
                    <property name="name">TRADETYPEINDEX</property>
                    <property name="nativeName">TRADETYPEINDEX</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">97</property>
                    <property name="name">HOUSE_CONTACT</property>
                    <property name="nativeName">HOUSE_CONTACT</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">98</property>
                    <property name="name">CONF__TITLE</property>
                    <property name="nativeName">CONF__TITLE</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">99</property>
                    <property name="name">STYLE1C</property>
                    <property name="nativeName">STYLE1C</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">100</property>
                    <property name="name">SWAPCLRWCAP</property>
                    <property name="nativeName">SWAPCLRWCAP</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">101</property>
                    <property name="name">HIDEANNEX</property>
                    <property name="nativeName">HIDEANNEX</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">102</property>
                    <property name="name">BSWAP</property>
                    <property name="nativeName">BSWAP</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">103</property>
                    <property name="name">HOUSE_TEL</property>
                    <property name="nativeName">HOUSE_TEL</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">104</property>
                    <property name="name">RISKDESC</property>
                    <property name="nativeName">RISKDESC</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">105</property>
                    <property name="name">PRICE_UOM</property>
                    <property name="nativeName">PRICE_UOM</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">106</property>
                    <property name="name">SHOWSCHEDULE</property>
                    <property name="nativeName">SHOWSCHEDULE</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">107</property>
                    <property name="name">SHOWSTRIKEUNIT</property>
                    <property name="nativeName">SHOWSTRIKEUNIT</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">108</property>
                    <property name="name">SHOWFLOATING</property>
                    <property name="nativeName">SHOWFLOATING</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">109</property>
                    <property name="name">PREMIUMGRID</property>
                    <property name="nativeName">PREMIUMGRID</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">110</property>
                    <property name="name">SHOWDAYCONVENTIONS</property>
                    <property name="nativeName">SHOWDAYCONVENTIONS</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">111</property>
                    <property name="name">SHOWBUYERSELLER</property>
                    <property name="nativeName">SHOWBUYERSELLER</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">112</property>
                    <property name="name">SHOWFIRMNESS</property>
                    <property name="nativeName">SHOWFIRMNESS</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">113</property>
                    <property name="name">SHOWPUTCALL</property>
                    <property name="nativeName">SHOWPUTCALL</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">114</property>
                    <property name="name">SHOWCOMMODITY</property>
                    <property name="nativeName">SHOWCOMMODITY</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">115</property>
                    <property name="name">DELIVERYTYPE</property>
                    <property name="nativeName">DELIVERYTYPE</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">116</property>
                    <property name="name">SHOWCALCAGENT</property>
                    <property name="nativeName">SHOWCALCAGENT</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">117</property>
                    <property name="name">SHOWSETTLEMENTPRICE</property>
                    <property name="nativeName">SHOWSETTLEMENTPRICE</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[select * from VW_RPT_CNF_HDR where TNUM = ?]]></xml-property>
            <xml-property name="designerValues"><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<model:DesignValues xmlns:design="http://www.eclipse.org/datatools/connectivity/oda/design" xmlns:model="http://www.eclipse.org/birt/report/model/adapter/odaModel">
  <Version>2.0</Version>
  <design:ResultSets derivedMetaData="true">
    <design:resultSetDefinitions>
      <design:resultSetColumns>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C0_B_S</design:name>
              <design:position>1</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>1</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C0_B_S</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C0_B_S</design:label>
            <design:formattingHints>
              <design:displaySize>1</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C1_CDY1_ATTR1</design:name>
              <design:position>2</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C1_CDY1_ATTR1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C1_CDY1_ATTR1</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C2_CDYDESC1</design:name>
              <design:position>3</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>60</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C2_CDYDESC1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C2_CDYDESC1</design:label>
            <design:formattingHints>
              <design:displaySize>60</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C3_CDY1_RISKDESC</design:name>
              <design:position>4</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C3_CDY1_RISKDESC</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C3_CDY1_RISKDESC</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C4_FIX_METHD1</design:name>
              <design:position>5</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>13</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C4_FIX_METHD1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C4_FIX_METHD1</design:label>
            <design:formattingHints>
              <design:displaySize>13</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C5_MKT1</design:name>
              <design:position>6</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>14</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C5_MKT1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C5_MKT1</design:label>
            <design:formattingHints>
              <design:displaySize>14</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C6_PSET1</design:name>
              <design:position>7</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C6_PSET1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C6_PSET1</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C8_AMT_PERIOD</design:name>
              <design:position>8</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C8_AMT_PERIOD</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C8_AMT_PERIOD</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C10_ZKEY</design:name>
              <design:position>9</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C10_ZKEY</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C10_ZKEY</design:label>
            <design:formattingHints>
              <design:displaySize>11</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C11_NUMBLKS</design:name>
              <design:position>10</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C11_NUMBLKS</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C11_NUMBLKS</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C12_BRO_DESCR</design:name>
              <design:position>11</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>128</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C12_BRO_DESCR</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C12_BRO_DESCR</design:label>
            <design:formattingHints>
              <design:displaySize>128</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C13_BROKER</design:name>
              <design:position>12</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C13_BROKER</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C13_BROKER</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C14_CDYDESC1</design:name>
              <design:position>13</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>60</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C14_CDYDESC1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C14_CDYDESC1</design:label>
            <design:formattingHints>
              <design:displaySize>60</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C15_CDYDESC2</design:name>
              <design:position>14</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>60</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C15_CDYDESC2</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C15_CDYDESC2</design:label>
            <design:formattingHints>
              <design:displaySize>60</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C16_CDY1</design:name>
              <design:position>15</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>6</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C16_CDY1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C16_CDY1</design:label>
            <design:formattingHints>
              <design:displaySize>6</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C17_CDYDESC2</design:name>
              <design:position>16</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>60</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C17_CDYDESC2</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C17_CDYDESC2</design:label>
            <design:formattingHints>
              <design:displaySize>60</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C18_CDY1</design:name>
              <design:position>17</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>6</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C18_CDY1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C18_CDY1</design:label>
            <design:formattingHints>
              <design:displaySize>6</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C19_CDY2</design:name>
              <design:position>18</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>6</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C19_CDY2</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C19_CDY2</design:label>
            <design:formattingHints>
              <design:displaySize>6</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C20_CPTY</design:name>
              <design:position>19</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C20_CPTY</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C20_CPTY</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C21_CPTY_ADDR1</design:name>
              <design:position>20</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C21_CPTY_ADDR1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C21_CPTY_ADDR1</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C22_CPTY_ADDR2</design:name>
              <design:position>21</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C22_CPTY_ADDR2</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C22_CPTY_ADDR2</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C23_CPTY_ADDR3</design:name>
              <design:position>22</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C23_CPTY_ADDR3</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C23_CPTY_ADDR3</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C24_CPTY_ADDR4</design:name>
              <design:position>23</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C24_CPTY_ADDR4</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C24_CPTY_ADDR4</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C25_CPTY_DESCR</design:name>
              <design:position>24</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>128</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C25_CPTY_DESCR</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C25_CPTY_DESCR</design:label>
            <design:formattingHints>
              <design:displaySize>128</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C26_CPTY_FAX</design:name>
              <design:position>25</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>24</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C26_CPTY_FAX</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C26_CPTY_FAX</design:label>
            <design:formattingHints>
              <design:displaySize>24</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C27_CPTY_PHONE</design:name>
              <design:position>26</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>24</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C27_CPTY_PHONE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C27_CPTY_PHONE</design:label>
            <design:formattingHints>
              <design:displaySize>24</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C28_CPTY_EMAIL</design:name>
              <design:position>27</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>50</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C28_CPTY_EMAIL</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C28_CPTY_EMAIL</design:label>
            <design:formattingHints>
              <design:displaySize>50</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C29_DELDATEEND_STR</design:name>
              <design:position>28</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C29_DELDATEEND_STR</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C29_DELDATEEND_STR</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C30_DELDATESTART_STR</design:name>
              <design:position>29</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C30_DELDATESTART_STR</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C30_DELDATESTART_STR</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C31_DELDATEEND_STR</design:name>
              <design:position>30</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C31_DELDATEEND_STR</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C31_DELDATEEND_STR</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C32_FIRMNESS</design:name>
              <design:position>31</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>24</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C32_FIRMNESS</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C32_FIRMNESS</design:label>
            <design:formattingHints>
              <design:displaySize>24</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C33_FIX_METHD2</design:name>
              <design:position>32</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>13</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C33_FIX_METHD2</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C33_FIX_METHD2</design:label>
            <design:formattingHints>
              <design:displaySize>13</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C34_FIX_METHD</design:name>
              <design:position>33</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>16</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C34_FIX_METHD</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C34_FIX_METHD</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C35_FLOAT_MULT</design:name>
              <design:position>34</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C35_FLOAT_MULT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C35_FLOAT_MULT</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C36_FLOAT_MULT</design:name>
              <design:position>35</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C36_FLOAT_MULT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C36_FLOAT_MULT</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C37_FLOAT_MULT</design:name>
              <design:position>36</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C37_FLOAT_MULT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C37_FLOAT_MULT</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C38_HOURALL</design:name>
              <design:position>37</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C38_HOURALL</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C38_HOURALL</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C39_HOUSE_ADDR1</design:name>
              <design:position>38</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C39_HOUSE_ADDR1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C39_HOUSE_ADDR1</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C40_HOUSE_ADDR2</design:name>
              <design:position>39</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C40_HOUSE_ADDR2</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C40_HOUSE_ADDR2</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C41_HOUSE_ADDR4</design:name>
              <design:position>40</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C41_HOUSE_ADDR4</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C41_HOUSE_ADDR4</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C42_HOUSEFULLNAME</design:name>
              <design:position>41</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>128</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C42_HOUSEFULLNAME</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C42_HOUSEFULLNAME</design:label>
            <design:formattingHints>
              <design:displaySize>128</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C43_HOUSEFULLNAME</design:name>
              <design:position>42</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>128</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C43_HOUSEFULLNAME</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C43_HOUSEFULLNAME</design:label>
            <design:formattingHints>
              <design:displaySize>128</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C44_HOUSE_ID</design:name>
              <design:position>43</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C44_HOUSE_ID</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C44_HOUSE_ID</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C45_MA_ATTR1</design:name>
              <design:position>44</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C45_MA_ATTR1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C45_MA_ATTR1</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C46_MA_CON_TYPE</design:name>
              <design:position>45</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C46_MA_CON_TYPE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C46_MA_CON_TYPE</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C47_MA_DESCRIPTION</design:name>
              <design:position>46</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C47_MA_DESCRIPTION</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C47_MA_DESCRIPTION</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C48_MA_EFFECTIVE_DT</design:name>
              <design:position>47</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>93</design:nativeDataTypeCode>
            <design:precision>7</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C48_MA_EFFECTIVE_DT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C48_MA_EFFECTIVE_DT</design:label>
            <design:formattingHints>
              <design:displaySize>7</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C49_MA_EXPIRATION_DT</design:name>
              <design:position>48</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>93</design:nativeDataTypeCode>
            <design:precision>7</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C49_MA_EXPIRATION_DT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C49_MA_EXPIRATION_DT</design:label>
            <design:formattingHints>
              <design:displaySize>7</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C50_MANUM</design:name>
              <design:position>49</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>15</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C50_MANUM</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C50_MANUM</design:label>
            <design:formattingHints>
              <design:displaySize>15</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C51_MKT1</design:name>
              <design:position>50</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>14</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C51_MKT1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C51_MKT1</design:label>
            <design:formattingHints>
              <design:displaySize>14</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C52_MKT2</design:name>
              <design:position>51</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>6</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C52_MKT2</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C52_MKT2</design:label>
            <design:formattingHints>
              <design:displaySize>6</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C53_OPTMODEL</design:name>
              <design:position>52</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C53_OPTMODEL</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C53_OPTMODEL</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C54_OPT_TYPE</design:name>
              <design:position>53</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>12</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C54_OPT_TYPE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C54_OPT_TYPE</design:label>
            <design:formattingHints>
              <design:displaySize>12</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C55_PHYS_BUYER2_ALT</design:name>
              <design:position>54</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>128</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C55_PHYS_BUYER2_ALT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C55_PHYS_BUYER2_ALT</design:label>
            <design:formattingHints>
              <design:displaySize>128</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C56_PHYS_BUYER2</design:name>
              <design:position>55</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C56_PHYS_BUYER2</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C56_PHYS_BUYER2</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C57_PHYS_SELLER2_ALT</design:name>
              <design:position>56</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>128</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C57_PHYS_SELLER2_ALT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C57_PHYS_SELLER2_ALT</design:label>
            <design:formattingHints>
              <design:displaySize>128</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C58_PHYS_SELLER2</design:name>
              <design:position>57</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C58_PHYS_SELLER2</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C58_PHYS_SELLER2</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C59_CDY1_POWER_REGION</design:name>
              <design:position>58</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>3</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C59_CDY1_POWER_REGION</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C59_CDY1_POWER_REGION</design:label>
            <design:formattingHints>
              <design:displaySize>3</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C60_PRC_PERIOD</design:name>
              <design:position>59</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C60_PRC_PERIOD</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C60_PRC_PERIOD</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C61_PRC_UNIT</design:name>
              <design:position>60</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C61_PRC_UNIT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C61_PRC_UNIT</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C62_PREM_DATE</design:name>
              <design:position>61</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>93</design:nativeDataTypeCode>
            <design:precision>7</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C62_PREM_DATE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C62_PREM_DATE</design:label>
            <design:formattingHints>
              <design:displaySize>7</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C63_PREM_PERIOD</design:name>
              <design:position>62</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C63_PREM_PERIOD</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C63_PREM_PERIOD</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C64_PREM_RATE</design:name>
              <design:position>63</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C64_PREM_RATE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C64_PREM_RATE</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C65_PREM_UNIT</design:name>
              <design:position>64</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C65_PREM_UNIT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C65_PREM_UNIT</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C66_PREMIUM</design:name>
              <design:position>65</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C66_PREMIUM</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C66_PREMIUM</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C67_STRIKE</design:name>
              <design:position>66</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C67_STRIKE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C67_STRIKE</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C68_PRC_PERIOD</design:name>
              <design:position>67</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C68_PRC_PERIOD</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C68_PRC_PERIOD</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C69_PRICE_STRIKE</design:name>
              <design:position>68</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C69_PRICE_STRIKE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C69_PRICE_STRIKE</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C70_PRC_UNIT</design:name>
              <design:position>69</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C70_PRC_UNIT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C70_PRC_UNIT</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C71_PSET1</design:name>
              <design:position>70</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C71_PSET1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C71_PSET1</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C72_PSET2</design:name>
              <design:position>71</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C72_PSET2</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C72_PSET2</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C73_PSET_DESC1</design:name>
              <design:position>72</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>24</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C73_PSET_DESC1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C73_PSET_DESC1</design:label>
            <design:formattingHints>
              <design:displaySize>24</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C74_PUTCALL</design:name>
              <design:position>73</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>4</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C74_PUTCALL</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C74_PUTCALL</design:label>
            <design:formattingHints>
              <design:displaySize>4</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C76_AMT_PERIOD</design:name>
              <design:position>74</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C76_AMT_PERIOD</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C76_AMT_PERIOD</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C77_UOM</design:name>
              <design:position>75</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C77_UOM</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C77_UOM</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C79_REPORT_DATE</design:name>
              <design:position>76</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>93</design:nativeDataTypeCode>
            <design:precision>7</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C79_REPORT_DATE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C79_REPORT_DATE</design:label>
            <design:formattingHints>
              <design:displaySize>7</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C80_DELDATESTART_STR</design:name>
              <design:position>77</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C80_DELDATESTART_STR</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C80_DELDATESTART_STR</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C81_DELDATESTART_STR</design:name>
              <design:position>78</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C81_DELDATESTART_STR</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C81_DELDATESTART_STR</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C82_PRICE_STRIKE</design:name>
              <design:position>79</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>49</design:precision>
            <design:scale>-127</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C82_PRICE_STRIKE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C82_PRICE_STRIKE</design:label>
            <design:formattingHints>
              <design:displaySize>16</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C85_TNUM</design:name>
              <design:position>80</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>6</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C85_TNUM</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C85_TNUM</design:label>
            <design:formattingHints>
              <design:displaySize>6</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C87_TRADE_DATE</design:name>
              <design:position>81</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>93</design:nativeDataTypeCode>
            <design:precision>7</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C87_TRADE_DATE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C87_TRADE_DATE</design:label>
            <design:formattingHints>
              <design:displaySize>7</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C88_TRADE_TEMPLATE</design:name>
              <design:position>82</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C88_TRADE_TEMPLATE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C88_TRADE_TEMPLATE</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C89_TRADE_TEMPLATE</design:name>
              <design:position>83</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C89_TRADE_TEMPLATE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C89_TRADE_TEMPLATE</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C90_TRADE_TYPE2</design:name>
              <design:position>84</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>31</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C90_TRADE_TYPE2</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C90_TRADE_TYPE2</design:label>
            <design:formattingHints>
              <design:displaySize>31</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C92_ZKEY</design:name>
              <design:position>85</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C92_ZKEY</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C92_ZKEY</design:label>
            <design:formattingHints>
              <design:displaySize>11</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C93_LOGONAME</design:name>
              <design:position>86</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C93_LOGONAME</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C93_LOGONAME</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C94_ERCOTDESC</design:name>
              <design:position>87</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>60</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C94_ERCOTDESC</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C94_ERCOTDESC</design:label>
            <design:formattingHints>
              <design:displaySize>60</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C95_CALCAGENT</design:name>
              <design:position>88</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>10</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C95_CALCAGENT</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C95_CALCAGENT</design:label>
            <design:formattingHints>
              <design:displaySize>10</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C96_FIXPAYOR</design:name>
              <design:position>89</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>12</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C96_FIXPAYOR</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C96_FIXPAYOR</design:label>
            <design:formattingHints>
              <design:displaySize>12</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C97_FLOATPAYOR</design:name>
              <design:position>90</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>12</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C97_FLOATPAYOR</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C97_FLOATPAYOR</design:label>
            <design:formattingHints>
              <design:displaySize>12</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C98_CAPCITYPWR</design:name>
              <design:position>91</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>8</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C98_CAPCITYPWR</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C98_CAPCITYPWR</design:label>
            <design:formattingHints>
              <design:displaySize>8</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C99_CAPCITYPWR2</design:name>
              <design:position>92</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>8</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C99_CAPCITYPWR2</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C99_CAPCITYPWR2</design:label>
            <design:formattingHints>
              <design:displaySize>8</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C100_FIRMPROPCASE</design:name>
              <design:position>93</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>384</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C100_FIRMPROPCASE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C100_FIRMPROPCASE</design:label>
            <design:formattingHints>
              <design:displaySize>384</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C101_SIDE1</design:name>
              <design:position>94</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>90</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C101_SIDE1</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C101_SIDE1</design:label>
            <design:formattingHints>
              <design:displaySize>90</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C102_SIDE2</design:name>
              <design:position>95</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>87</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C102_SIDE2</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C102_SIDE2</design:label>
            <design:formattingHints>
              <design:displaySize>87</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>C103_TRADETYPEINDEX</design:name>
              <design:position>96</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>31</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>C103_TRADETYPEINDEX</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>C103_TRADETYPEINDEX</design:label>
            <design:formattingHints>
              <design:displaySize>31</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
      </design:resultSetColumns>
      <design:criteria/>
    </design:resultSetDefinitions>
  </design:ResultSets>
</model:DesignValues>]]></xml-property>
        </oda-data-set>
        <oda-data-set extensionID="org.eclipse.birt.report.data.oda.jdbc.JdbcSelectDataSet" name="Data Set 2" id="400">
            <list-property name="columnHints">
                <structure>
                    <property name="columnName">KEY_TNUM</property>
                    <property name="alias">KEY_TNUM</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">KEY_TNUM</text-property>
                    <text-property name="heading">KEY_TNUM</text-property>
                </structure>
                <structure>
                    <property name="columnName">PAYDATE</property>
                    <property name="alias">PAYDATE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">PAYDATE</text-property>
                    <text-property name="heading">PAYDATE</text-property>
                </structure>
                <structure>
                    <property name="columnName">DELDAY</property>
                    <property name="alias">DELDAY</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">DELDAY</text-property>
                    <text-property name="heading">DELDAY</text-property>
                </structure>
                <structure>
                    <property name="columnName">HFLAG</property>
                    <property name="alias">HFLAG</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">HFLAG</text-property>
                    <text-property name="heading">HFLAG</text-property>
                </structure>
                <structure>
                    <property name="columnName">PERIOD</property>
                    <property name="alias">PERIOD</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">PERIOD</text-property>
                    <text-property name="heading">PERIOD</text-property>
                </structure>
                <structure>
                    <property name="columnName">DDAY</property>
                    <property name="alias">DDAY</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">DDAY</text-property>
                    <text-property name="heading">DDAY</text-property>
                </structure>
                <structure>
                    <property name="columnName">TIMES</property>
                    <property name="alias">TIMES</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">TIMES</text-property>
                    <text-property name="heading">TIMES</text-property>
                </structure>
                <structure>
                    <property name="columnName">TIMEE</property>
                    <property name="alias">TIMEE</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">TIMEE</text-property>
                    <text-property name="heading">TIMEE</text-property>
                </structure>
                <structure>
                    <property name="columnName">TIMES_</property>
                    <property name="alias">TIMES_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TIMES_</text-property>
                    <text-property name="heading">TIMES_</text-property>
                </structure>
                <structure>
                    <property name="columnName">TIMEE_</property>
                    <property name="alias">TIMEE_</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TIMEE_</text-property>
                    <text-property name="heading">TIMEE_</text-property>
                </structure>
                <structure>
                    <property name="columnName">LONGDAYFLAG</property>
                    <property name="alias">LONGDAYFLAG</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">LONGDAYFLAG</text-property>
                    <text-property name="heading">LONGDAYFLAG</text-property>
                </structure>
                <structure>
                    <property name="columnName">QTY</property>
                    <property name="alias">QTY</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">QTY</text-property>
                    <text-property name="heading">QTY</text-property>
                </structure>
                <structure>
                    <property name="columnName">PRC</property>
                    <property name="alias">PRC</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">PRC</text-property>
                    <text-property name="heading">PRC</text-property>
                </structure>
                <structure>
                    <property name="columnName">POWSEG</property>
                    <property name="alias">POWSEG</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">POWSEG</text-property>
                    <text-property name="heading">POWSEG</text-property>
                </structure>
                <structure>
                    <property name="columnName">ENDDAY</property>
                    <property name="alias">ENDDAY</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">ENDDAY</text-property>
                    <text-property name="heading">ENDDAY</text-property>
                </structure>
                <structure>
                    <property name="columnName">AMT_PERIOD</property>
                    <property name="alias">AMT_PERIOD</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">AMT_PERIOD</text-property>
                    <text-property name="heading">AMT_PERIOD</text-property>
                </structure>
                <structure>
                    <property name="columnName">AMT_UNIT</property>
                    <property name="alias">AMT_UNIT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">AMT_UNIT</text-property>
                    <text-property name="heading">AMT_UNIT</text-property>
                </structure>
                <structure>
                    <property name="columnName">CCY</property>
                    <property name="alias">CCY</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">CCY</text-property>
                    <text-property name="heading">CCY</text-property>
                </structure>
                <structure>
                    <property name="columnName">UNIT</property>
                    <property name="alias">UNIT</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">UNIT</text-property>
                    <text-property name="heading">UNIT</text-property>
                </structure>
                <structure>
                    <property name="columnName">TS</property>
                    <property name="alias">TS</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TS</text-property>
                    <text-property name="heading">TS</text-property>
                </structure>
                <structure>
                    <property name="columnName">TE</property>
                    <property name="alias">TE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TE</text-property>
                    <text-property name="heading">TE</text-property>
                </structure>
                <structure>
                    <property name="columnName">M2</property>
                    <property name="alias">M2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">M2</text-property>
                    <text-property name="heading">M2</text-property>
                </structure>
                <structure>
                    <property name="columnName">TS_STARTTIME</property>
                    <property name="alias">TS_STARTTIME</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TS_STARTTIME</text-property>
                    <text-property name="heading">TS_STARTTIME</text-property>
                </structure>
                <structure>
                    <property name="columnName">TS_ENDTIME</property>
                    <property name="alias">TS_ENDTIME</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">TS_ENDTIME</text-property>
                    <text-property name="heading">TS_ENDTIME</text-property>
                </structure>
                <structure>
                    <property name="columnName">RTOT</property>
                    <property name="alias">RTOT</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">RTOT</text-property>
                    <text-property name="heading">RTOT</text-property>
                </structure>
                <structure>
                    <property name="columnName">AUD_REFRESH</property>
                    <property name="alias">AUD_REFRESH</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">AUD_REFRESH</text-property>
                    <text-property name="heading">AUD_REFRESH</text-property>
                </structure>
                <structure>
                    <property name="columnName">RID</property>
                    <property name="alias">RID</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">RID</text-property>
                    <text-property name="heading">RID</text-property>
                </structure>
                <structure>
                    <property name="columnName">AUD_GEN</property>
                    <property name="alias">AUD_GEN</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">AUD_GEN</text-property>
                    <text-property name="heading">AUD_GEN</text-property>
                </structure>
                <structure>
                    <property name="columnName">QTYTOT</property>
                    <property name="alias">QTYTOT</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">QTYTOT</text-property>
                    <text-property name="heading">QTYTOT</text-property>
                </structure>
                <structure>
                    <property name="columnName">INVOICE_AMOUNT</property>
                    <property name="alias">INVOICE_AMOUNT</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">INVOICE_AMOUNT</text-property>
                    <text-property name="heading">INVOICE_AMOUNT</text-property>
                </structure>
                <structure>
                    <property name="columnName">QTY_ABS</property>
                    <property name="alias">QTY_ABS</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">QTY_ABS</text-property>
                    <text-property name="heading">QTY_ABS</text-property>
                </structure>
                <structure>
                    <property name="columnName">INVOICE_AMOUNT_ABS</property>
                    <property name="alias">INVOICE_AMOUNT_ABS</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">INVOICE_AMOUNT_ABS</text-property>
                    <text-property name="heading">INVOICE_AMOUNT_ABS</text-property>
                </structure>
                <structure>
                    <property name="columnName">ZKEY</property>
                    <property name="alias">ZKEY</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">ZKEY</text-property>
                    <text-property name="heading">ZKEY</text-property>
                </structure>
                <structure>
                    <property name="columnName">CAP_ABS</property>
                    <property name="alias">CAP_ABS</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">CAP_ABS</text-property>
                    <text-property name="heading">CAP_ABS</text-property>
                </structure>
                <structure>
                    <property name="columnName">DELDAYS</property>
                    <property name="alias">DELDAYS</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">DELDAYS</text-property>
                    <text-property name="heading">DELDAYS</text-property>
                </structure>
                <structure>
                    <property name="columnName">EXPIRY</property>
                    <property name="alias">EXPIRY</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">EXPIRY</text-property>
                    <text-property name="heading">EXPIRY</text-property>
                </structure>
                <structure>
                    <property name="columnName">PRICE_TRD</property>
                    <property name="alias">PRICE_TRD</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">PRICE_TRD</text-property>
                    <text-property name="heading">PRICE_TRD</text-property>
                </structure>
                <structure>
                    <property name="columnName">FLOAT_MULT</property>
                    <property name="alias">FLOAT_MULT</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">FLOAT_MULT</text-property>
                    <text-property name="heading">FLOAT_MULT</text-property>
                </structure>
            </list-property>
            <list-property name="parameters">
                <structure>
                    <property name="name">param_1</property>
                    <property name="paramName">TNUM</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                    <property name="position">1</property>
                    <property name="isOptional">true</property>
                    <property name="allowNull">true</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
            </list-property>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">KEY_TNUM</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">2</property>
                        <property name="name">PAYDATE</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">3</property>
                        <property name="name">DELDAY</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">4</property>
                        <property name="name">HFLAG</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">5</property>
                        <property name="name">PERIOD</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">6</property>
                        <property name="name">DDAY</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">7</property>
                        <property name="name">TIMES</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">8</property>
                        <property name="name">TIMEE</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">9</property>
                        <property name="name">TIMES_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">10</property>
                        <property name="name">TIMEE_</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">11</property>
                        <property name="name">LONGDAYFLAG</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">12</property>
                        <property name="name">QTY</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">13</property>
                        <property name="name">PRC</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">14</property>
                        <property name="name">POWSEG</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">15</property>
                        <property name="name">ENDDAY</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">16</property>
                        <property name="name">AMT_PERIOD</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">17</property>
                        <property name="name">AMT_UNIT</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">18</property>
                        <property name="name">CCY</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">19</property>
                        <property name="name">UNIT</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">20</property>
                        <property name="name">TS</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">21</property>
                        <property name="name">TE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">22</property>
                        <property name="name">M2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">23</property>
                        <property name="name">TS_STARTTIME</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">24</property>
                        <property name="name">TS_ENDTIME</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">25</property>
                        <property name="name">RTOT</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">26</property>
                        <property name="name">AUD_REFRESH</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">27</property>
                        <property name="name">RID</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">28</property>
                        <property name="name">AUD_GEN</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">29</property>
                        <property name="name">QTYTOT</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">30</property>
                        <property name="name">INVOICE_AMOUNT</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">31</property>
                        <property name="name">QTY_ABS</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">32</property>
                        <property name="name">INVOICE_AMOUNT_ABS</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">33</property>
                        <property name="name">ZKEY</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">34</property>
                        <property name="name">CAP_ABS</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">35</property>
                        <property name="name">DELDAYS</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">36</property>
                        <property name="name">EXPIRY</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">37</property>
                        <property name="name">PRICE_TRD</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">38</property>
                        <property name="name">FLOAT_MULT</property>
                        <property name="dataType">decimal</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">Data Source</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">KEY_TNUM</property>
                    <property name="nativeName">KEY_TNUM</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">2</property>
                    <property name="name">PAYDATE</property>
                    <property name="nativeName">PAYDATE</property>
                    <property name="dataType">date-time</property>
                </structure>
                <structure>
                    <property name="position">3</property>
                    <property name="name">DELDAY</property>
                    <property name="nativeName">DELDAY</property>
                    <property name="dataType">date-time</property>
                </structure>
                <structure>
                    <property name="position">4</property>
                    <property name="name">HFLAG</property>
                    <property name="nativeName">HFLAG</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">5</property>
                    <property name="name">PERIOD</property>
                    <property name="nativeName">PERIOD</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">6</property>
                    <property name="name">DDAY</property>
                    <property name="nativeName">DDAY</property>
                    <property name="dataType">date-time</property>
                </structure>
                <structure>
                    <property name="position">7</property>
                    <property name="name">TIMES</property>
                    <property name="nativeName">TIMES</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">8</property>
                    <property name="name">TIMEE</property>
                    <property name="nativeName">TIMEE</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">9</property>
                    <property name="name">TIMES_</property>
                    <property name="nativeName">TIMES_</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">10</property>
                    <property name="name">TIMEE_</property>
                    <property name="nativeName">TIMEE_</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">11</property>
                    <property name="name">LONGDAYFLAG</property>
                    <property name="nativeName">LONGDAYFLAG</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">12</property>
                    <property name="name">QTY</property>
                    <property name="nativeName">QTY</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">13</property>
                    <property name="name">PRC</property>
                    <property name="nativeName">PRC</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">14</property>
                    <property name="name">POWSEG</property>
                    <property name="nativeName">POWSEG</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">15</property>
                    <property name="name">ENDDAY</property>
                    <property name="nativeName">ENDDAY</property>
                    <property name="dataType">date-time</property>
                </structure>
                <structure>
                    <property name="position">16</property>
                    <property name="name">AMT_PERIOD</property>
                    <property name="nativeName">AMT_PERIOD</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">17</property>
                    <property name="name">AMT_UNIT</property>
                    <property name="nativeName">AMT_UNIT</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">18</property>
                    <property name="name">CCY</property>
                    <property name="nativeName">CCY</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">19</property>
                    <property name="name">UNIT</property>
                    <property name="nativeName">UNIT</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">20</property>
                    <property name="name">TS</property>
                    <property name="nativeName">TS</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">21</property>
                    <property name="name">TE</property>
                    <property name="nativeName">TE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">22</property>
                    <property name="name">M2</property>
                    <property name="nativeName">M2</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">23</property>
                    <property name="name">TS_STARTTIME</property>
                    <property name="nativeName">TS_STARTTIME</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">24</property>
                    <property name="name">TS_ENDTIME</property>
                    <property name="nativeName">TS_ENDTIME</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">25</property>
                    <property name="name">RTOT</property>
                    <property name="nativeName">RTOT</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">26</property>
                    <property name="name">AUD_REFRESH</property>
                    <property name="nativeName">AUD_REFRESH</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">27</property>
                    <property name="name">RID</property>
                    <property name="nativeName">RID</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">28</property>
                    <property name="name">AUD_GEN</property>
                    <property name="nativeName">AUD_GEN</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">29</property>
                    <property name="name">QTYTOT</property>
                    <property name="nativeName">QTYTOT</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">30</property>
                    <property name="name">INVOICE_AMOUNT</property>
                    <property name="nativeName">INVOICE_AMOUNT</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">31</property>
                    <property name="name">QTY_ABS</property>
                    <property name="nativeName">QTY_ABS</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">32</property>
                    <property name="name">INVOICE_AMOUNT_ABS</property>
                    <property name="nativeName">INVOICE_AMOUNT_ABS</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">33</property>
                    <property name="name">ZKEY</property>
                    <property name="nativeName">ZKEY</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">34</property>
                    <property name="name">CAP_ABS</property>
                    <property name="nativeName">CAP_ABS</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">35</property>
                    <property name="name">DELDAYS</property>
                    <property name="nativeName">DELDAYS</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">36</property>
                    <property name="name">EXPIRY</property>
                    <property name="nativeName">EXPIRY</property>
                    <property name="dataType">date-time</property>
                </structure>
                <structure>
                    <property name="position">37</property>
                    <property name="name">PRICE_TRD</property>
                    <property name="nativeName">PRICE_TRD</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">38</property>
                    <property name="name">FLOAT_MULT</property>
                    <property name="nativeName">FLOAT_MULT</property>
                    <property name="dataType">decimal</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[select * from RPT_CNF_DETAILS where KEY_TNUM = ?]]></xml-property>
            <xml-property name="designerValues"><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<model:DesignValues xmlns:design="http://www.eclipse.org/datatools/connectivity/oda/design" xmlns:model="http://www.eclipse.org/birt/report/model/adapter/odaModel">
  <Version>2.0</Version>
  <DataSetParameters>
    <parameter>
      <design:ParameterDefinition>
        <design:inOutMode>In</design:inOutMode>
        <design:attributes>
          <design:identifier>
            <design:name></design:name>
            <design:position>1</design:position>
          </design:identifier>
          <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
          <design:precision>6</design:precision>
          <design:scale>0</design:scale>
          <design:nullability>Nullable</design:nullability>
        </design:attributes>
        <design:inputAttributes>
          <design:elementAttributes>
            <design:optional>true</design:optional>
          </design:elementAttributes>
        </design:inputAttributes>
      </design:ParameterDefinition>
    </parameter>
  </DataSetParameters>
</model:DesignValues>]]></xml-property>
        </oda-data-set>
    </data-sets>
    <styles>
        <style name="report" id="736">
            <structure name="dateTimeFormat">
                <property name="category">Custom</property>
                <property name="pattern">MM/dd/yyyy hh:mm:s a</property>
            </structure>
        </style>
    </styles>
    <page-setup>
        <simple-master-page name="Simple MasterPage" id="2">
            <page-footer>
                <text id="3">
                    <property name="contentType">html</property>
                    <text-property name="content"><![CDATA[<value-of>new Date()</value-of>]]></text-property>
                </text>
            </page-footer>
        </simple-master-page>
    </page-setup>
    <body>
        <grid name="Logo and Sender Information" id="11">
            <property name="height">1.9166666666666667in</property>
            <property name="width">7.947916666666667in</property>
            <column id="12">
                <property name="width">5.135416666666667in</property>
            </column>
            <column id="13">
                <property name="width">2.8125in</property>
            </column>
            <row id="14">
                <property name="height">1.9166666666666667in</property>
                <cell id="15">
                    <image name="NRGLogo" id="676">
                        <property name="height">1.0416666666666667in</property>
                        <property name="width">1.5208333333333333in</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="visibility">
                            <structure>
                                <property name="format">all</property>
                                <expression name="valueExpr" type="javascript">row["LOGONAME"].trim() == "DIRECTLogo"</expression>
                            </structure>
                        </list-property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">LOGONAME</property>
                                <text-property name="displayName">LOGONAME</text-property>
                                <expression name="expression" type="javascript">dataSetRow["LOGONAME"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="source">file</property>
                        <expression name="uri" type="constant">nrg.png</expression>
                    </image>
                    <image name="DIRECTLogo" id="244">
                        <property name="height">1in</property>
                        <property name="width">2.1354166666666665in</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="visibility">
                            <structure>
                                <property name="format">all</property>
                                <expression name="valueExpr" type="javascript">row["LOGONAME"].trim() == "NRGLogo"</expression>
                            </structure>
                        </list-property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">LOGONAME</property>
                                <text-property name="displayName">LOGONAME</text-property>
                                <expression name="expression" type="javascript">dataSetRow["LOGONAME"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="source">file</property>
                        <expression name="uri" type="constant">direct-energy.png</expression>
                    </image>
                </cell>
                <cell id="16">
                    <data id="621">
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">HOUSE_ADDR1</property>
                                <text-property name="displayName">HOUSE_ADDR1</text-property>
                                <expression name="expression" type="javascript">dataSetRow["HOUSE_ADDR1"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">HOUSE_ADDR1</property>
                    </data>
                    <data id="622">
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">HOUSE_ADDR2</property>
                                <text-property name="displayName">HOUSE_ADDR2</text-property>
                                <expression name="expression" type="javascript">dataSetRow["HOUSE_ADDR2"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">HOUSE_ADDR2</property>
                    </data>
                </cell>
            </row>
        </grid>
        <text id="623">
            <property name="textAlign">center</property>
            <property name="contentType">auto</property>
            <text-property name="content"><![CDATA[SWAP CONFIRMATION]]></text-property>
        </text>
        <grid name="Receiver Information" id="38">
            <property name="width">7.947916666666667in</property>
            <column id="39">
                <property name="width">1.3854166666666667in</property>
            </column>
            <column id="40">
                <property name="width">6.5625in</property>
            </column>
            <row id="41">
                <cell id="42">
                    <text id="53">
                        <property name="fontSize">10pt</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[Date:]]></text-property>
                    </text>
                </cell>
                <cell id="43">
                    <data id="401">
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">REPORT_DATE</property>
                                <text-property name="displayName">REPORT_DATE</text-property>
                                <expression name="expression" type="javascript">dataSetRow["REPORT_DATE"]</expression>
                                <property name="dataType">date-time</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">REPORT_DATE</property>
                    </data>
                </cell>
            </row>
            <row id="44">
                <cell id="45">
                    <text id="54">
                        <property name="fontSize">10pt</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[To:]]></text-property>
                    </text>
                </cell>
                <cell id="46">
                    <data id="402">
                        <property name="display">inline</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">CPTY_DESCR</property>
                                <text-property name="displayName">CPTY_DESCR</text-property>
                                <expression name="expression" type="javascript">dataSetRow["CPTY_DESCR"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">CPTY_DESCR</property>
                    </data>
                    <text id="60">
                        <property name="fontSize">10pt</property>
                        <property name="display">inline</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[("Counterparty")]]></text-property>
                    </text>
                </cell>
            </row>
            <row id="47">
                <cell id="48">
                    <text id="55">
                        <property name="fontSize">10pt</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[From:]]></text-property>
                    </text>
                </cell>
                <cell id="49">
                    <data id="403">
                        <property name="display">inline</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">HOUSEFULLNAME</property>
                                <text-property name="displayName">HOUSEFULLNAME</text-property>
                                <expression name="expression" type="javascript">dataSetRow["HOUSEFULLNAME"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">HOUSEFULLNAME</property>
                    </data>
                    <text id="626">
                        <property name="display">inline</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[ ("]]></text-property>
                    </text>
                    <data id="597">
                        <property name="display">inline</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">CALCAGENT</property>
                                <text-property name="displayName">CALCAGENT</text-property>
                                <expression name="expression" type="javascript">dataSetRow["CALCAGENT"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">CALCAGENT</property>
                    </data>
                    <text id="627">
                        <property name="display">inline</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[")]]></text-property>
                    </text>
                </cell>
            </row>
            <row id="50">
                <cell id="51">
                    <text id="56">
                        <property name="fontSize">10pt</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[Re:]]></text-property>
                    </text>
                </cell>
                <cell id="52">
                    <text id="57">
                        <property name="fontSize">10pt</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[Commodity Swap - Cash settled]]></text-property>
                    </text>
                </cell>
            </row>
        </grid>
        <text id="598">
            <property name="contentType">auto</property>
            <text-property name="content"><![CDATA[ ]]></text-property>
        </text>
        <grid name="Operative Clause" id="660">
            <column id="661"/>
            <row id="662">
                <cell id="663">
                    <text name="OperativeClause_Text" id="616">
                        <property name="display">inline</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[The purpose of this confirmation is to confirm the terms and conditions of the Transaction entered into between us on the Trade Date specified below (the "Transaction"). This confirmation constitutes a "Confirmation" as referred to in the ISDA Master Agreement specified below.
The definitions and provisions contained in the 2005 ISDA Commodity Definitions (the "Commodity Definitions") (as published by the International Swaps and Derivatives Association, Inc.), are incorporated into this Confirmation. In the event of any inconsistency between those definitions and provisions and this Confirmation, this  Confirmation will govern. This Confirmation supplements, forms part of and is subject to, the ISDA Master Agreement dated as of ]]></text-property>
                    </text>
                    <data id="624">
                        <structure name="dateTimeFormat">
                            <property name="category">Long Date</property>
                            <property name="pattern">Long Date</property>
                        </structure>
                        <property name="display">inline</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">MA_EFFECTIVE_DT</property>
                                <text-property name="displayName">MA_EFFECTIVE_DT</text-property>
                                <expression name="expression" type="javascript">dataSetRow["MA_EFFECTIVE_DT"]</expression>
                                <property name="dataType">date-time</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">MA_EFFECTIVE_DT</property>
                    </data>
                    <text id="625">
                        <property name="backgroundColor">#FFFFFF</property>
                        <property name="display">inline</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[ as amended and supplemented from time to time (the "Agreement"), between you and us. All provisions contained in the Agreement govern this Confirmation except as expressly modified below.]]></text-property>
                    </text>
                </cell>
            </row>
        </grid>
        <text id="599">
            <property name="contentType">auto</property>
            <text-property name="content"><![CDATA[ ]]></text-property>
        </text>
        <grid name="Derivatives Info" id="664">
            <column id="665"/>
            <row id="666">
                <cell id="667">
                    <text id="63">
                        <property name="fontSize">10pt</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[1.     The terms of the particular Transaction to which this Confirmation relates are as follows:]]></text-property>
                    </text>
                    <text id="64">
                        <property name="fontSize">10pt</property>
                        <property name="fontWeight">bold</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[2.     General Terms:]]></text-property>
                    </text>
                    <grid id="65">
                        <property name="width">7.947916666666667in</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">PRICE_UOM</property>
                                <text-property name="displayName">PRICE_UOM</text-property>
                                <expression name="expression" type="javascript">dataSetRow["PRICE_UOM"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                            <structure>
                                <property name="name">PRC_UNIT_</property>
                                <text-property name="displayName">PRC_UNIT_</text-property>
                                <expression name="expression" type="javascript">dataSetRow["PRC_UNIT_"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                            <structure>
                                <property name="name">SHOWDAYCONVENTIONS</property>
                                <text-property name="displayName">SHOWDAYCONVENTIONS</text-property>
                                <expression name="expression" type="javascript">dataSetRow["SHOWDAYCONVENTIONS"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                            <structure>
                                <property name="name">RISKDESC</property>
                                <text-property name="displayName">RISKDESC</text-property>
                                <expression name="expression" type="javascript">dataSetRow["RISKDESC"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <column id="66">
                            <property name="width">3.1041666666666665in</property>
                        </column>
                        <column id="67">
                            <property name="width">4.84375in</property>
                        </column>
                        <row id="68">
                            <cell id="69">
                                <property name="fontSize">8pt</property>
                                <text id="101">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Trade Date:]]></text-property>
                                </text>
                            </cell>
                            <cell id="70">
                                <property name="fontSize">8pt</property>
                                <data id="404">
                                    <property name="fontSize">10pt</property>
                                    <property name="dataSet">Data Set 1</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">TRADE_DATE</property>
                                            <text-property name="displayName">TRADE_DATE</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["TRADE_DATE"]</expression>
                                            <property name="dataType">date-time</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">TRADE_DATE</property>
                                </data>
                            </cell>
                        </row>
                        <row id="71">
                            <cell id="72">
                                <property name="fontSize">8pt</property>
                                <text id="102">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Effective Date:]]></text-property>
                                </text>
                            </cell>
                            <cell id="73">
                                <property name="fontSize">8pt</property>
                                <text id="116">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[The "Start Date" specified on Annex A]]></text-property>
                                </text>
                            </cell>
                        </row>
                        <row id="74">
                            <cell id="75">
                                <property name="fontSize">8pt</property>
                                <text id="103">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Termination Date:]]></text-property>
                                </text>
                            </cell>
                            <cell id="76">
                                <property name="fontSize">8pt</property>
                                <text id="115">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[The "End Date" specified on Annex A]]></text-property>
                                </text>
                            </cell>
                        </row>
                        <row id="77">
                            <cell id="78">
                                <property name="fontSize">8pt</property>
                                <text id="104">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Commodity:]]></text-property>
                                </text>
                            </cell>
                            <cell id="79">
                                <property name="fontSize">8pt</property>
                                <data id="678">
                                    <property name="fontSize">10pt</property>
                                    <property name="resultSetColumn">RISKDESC</property>
                                </data>
                            </cell>
                        </row>
                        <row id="80">
                            <cell id="81">
                                <property name="fontSize">8pt</property>
                                <text id="105">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Unit:]]></text-property>
                                </text>
                            </cell>
                            <cell id="82">
                                <property name="fontSize">8pt</property>
                                <data id="655">
                                    <property name="fontSize">10pt</property>
                                    <property name="display">inline</property>
                                    <property name="resultSetColumn">PRC_UNIT_</property>
                                </data>
                                <data id="653">
                                    <property name="fontSize">10pt</property>
                                    <property name="display">inline</property>
                                    <property name="resultSetColumn">PRICE_UOM</property>
                                </data>
                            </cell>
                        </row>
                        <row id="83">
                            <cell id="84">
                                <property name="fontSize">8pt</property>
                                <text id="106">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Total Notional Quantity:]]></text-property>
                                </text>
                            </cell>
                            <cell id="85">
                                <property name="fontSize">8pt</property>
                                <data id="596">
                                    <property name="fontSize">10pt</property>
                                    <property name="dataSet">Data Set 2</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">QTYTOT</property>
                                            <text-property name="displayName">QTYTOT</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["QTYTOT"]</expression>
                                            <property name="dataType">decimal</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">QTYTOT</property>
                                </data>
                            </cell>
                        </row>
                        <row id="86">
                            <cell id="87">
                                <property name="fontSize">8pt</property>
                                <text id="107">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Notional Quantity per Calculation Period:]]></text-property>
                                </text>
                            </cell>
                            <cell id="88">
                                <property name="fontSize">8pt</property>
                                <text id="120">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[As Per Annex A]]></text-property>
                                </text>
                            </cell>
                        </row>
                        <row id="89">
                            <cell id="90">
                                <property name="fontSize">8pt</property>
                                <text id="108">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Calculation Period(s):]]></text-property>
                                </text>
                            </cell>
                            <cell id="91">
                                <property name="fontSize">8pt</property>
                                <text id="121">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Each calendar month, from and including the Effective Date to and including the Termination Date.]]></text-property>
                                </text>
                            </cell>
                        </row>
                        <row id="92">
                            <cell id="93">
                                <property name="fontSize">8pt</property>
                                <text id="109">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Settlement Date(s):]]></text-property>
                                </text>
                            </cell>
                            <cell id="94">
                                <property name="fontSize">8pt</property>
                                <text id="122">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Ten (10) Business Days after the end of each Calculation Period]]></text-property>
                                </text>
                            </cell>
                        </row>
                        <row id="95">
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">true</expression>
                                </structure>
                            </list-property>
                            <cell id="96">
                                <property name="fontSize">8pt</property>
                                <text id="110">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Business Day:]]></text-property>
                                </text>
                            </cell>
                            <cell id="97">
                                <text id="123">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[New York, New York]]></text-property>
                                </text>
                            </cell>
                        </row>
                        <row id="638">
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">row["SHOWDAYCONVENTIONS"] == 'N'</expression>
                                </structure>
                            </list-property>
                            <cell id="639">
                                <property name="fontSize">8pt</property>
                                <text id="641">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Business Day Convention:]]></text-property>
                                </text>
                            </cell>
                            <cell id="640">
                                <text id="642">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[[Following]]]></text-property>
                                </text>
                            </cell>
                        </row>
                        <row id="628">
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">row["SHOWDAYCONVENTIONS"] == 'N'</expression>
                                </structure>
                            </list-property>
                            <cell id="629">
                                <property name="fontSize">8pt</property>
                                <text id="634">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Commodity Business Day Convention:]]></text-property>
                                </text>
                            </cell>
                            <cell id="630">
                                <text id="636">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[[Following]]]></text-property>
                                </text>
                            </cell>
                        </row>
                        <row id="98">
                            <cell id="99">
                                <text id="111">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Calculation Agent:]]></text-property>
                                </text>
                            </cell>
                            <cell id="100">
                                <data id="600">
                                    <property name="dataSet">Data Set 1</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">CALCAGENT</property>
                                            <text-property name="displayName">CALCAGENT</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["CALCAGENT"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">CALCAGENT</property>
                                </data>
                            </cell>
                        </row>
                    </grid>
                    <grid id="124">
                        <column id="125">
                            <property name="width">3.104in</property>
                        </column>
                        <column id="126"/>
                        <row id="127">
                            <cell id="128">
                                <text id="136">
                                    <property name="fontSize">10pt</property>
                                    <property name="fontWeight">bold</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Fixed Amount Details:]]></text-property>
                                </text>
                            </cell>
                            <cell id="129"/>
                        </row>
                        <row id="130">
                            <cell id="131">
                                <text id="137">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[	Fixed Price Payer:]]></text-property>
                                </text>
                            </cell>
                            <cell id="132">
                                <data id="601">
                                    <property name="dataSet">Data Set 1</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">FIXPAYOR</property>
                                            <text-property name="displayName">FIXPAYOR</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["FIXPAYOR"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">FIXPAYOR</property>
                                </data>
                            </cell>
                        </row>
                        <row id="133">
                            <cell id="134">
                                <text id="138">
                                    <property name="fontSize">10pt</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[	Fixed Price]]></text-property>
                                </text>
                            </cell>
                            <cell id="135">
                                <text id="406">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[$ per Unit of Commodity as Per Annex A]]></text-property>
                                </text>
                            </cell>
                        </row>
                        <row id="334">
                            <cell id="335">
                                <text id="337">
                                    <property name="fontWeight">bold</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Floating Amount Details]]></text-property>
                                </text>
                            </cell>
                            <cell id="336"/>
                        </row>
                        <row id="602">
                            <cell id="603">
                                <text id="605">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Floating Price Payer:]]></text-property>
                                </text>
                            </cell>
                            <cell id="604">
                                <data id="606">
                                    <property name="dataSet">Data Set 1</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">FLOATPAYOR</property>
                                            <text-property name="displayName">FLOATPAYOR</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["FLOATPAYOR"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">FLOATPAYOR</property>
                                </data>
                            </cell>
                        </row>
                        <row id="331">
                            <cell id="332">
                                <text id="338">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Floating Price]]></text-property>
                                </text>
                            </cell>
                            <cell id="333">
                                <text id="407">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[In respect of each Calculation Period, the average of all of the Commodity Reference Prices for each Pricing Date falling within such Calculation Period, and rounded in accordance with "Rounding of Floating Price" below.]]></text-property>
                                </text>
                            </cell>
                        </row>
                        <row id="348">
                            <cell id="349">
                                <text id="351">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Commodity Reference Price]]></text-property>
                                </text>
                            </cell>
                            <cell id="350">
                                <text id="408">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[For a Pricing Date, the Specified Price for such Pricing Date.]]></text-property>
                                </text>
                            </cell>
                        </row>
                        <row id="345">
                            <cell id="346">
                                <text id="352">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Currency]]></text-property>
                                </text>
                            </cell>
                            <cell id="347">
                                <text id="409">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[U.S. Dollars]]></text-property>
                                </text>
                            </cell>
                        </row>
                        <row id="342">
                            <cell id="343">
                                <text id="353">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Specified Price:]]></text-property>
                                </text>
                            </cell>
                            <cell id="344">
                                <text id="410">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[$ per Unit of Commodity as Per Annex A]]></text-property>
                                </text>
                            </cell>
                        </row>
                        <row id="339">
                            <cell id="340">
                                <text id="354">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Pricing Date(s):]]></text-property>
                                </text>
                            </cell>
                            <cell id="341">
                                <text id="411">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Each Commodity Business Day within a Calculation Period.]]></text-property>
                                </text>
                            </cell>
                        </row>
                        <row id="379">
                            <cell id="380">
                                <text id="382">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Rounding of the Floating Price:]]></text-property>
                                </text>
                            </cell>
                            <cell id="381">
                                <text id="412">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Floating Price shall be rounded to 4 decimal places.  If the 5th decimal number is five or greater, then the 4th decimal number shall be increased by one, and if the 5th decimal number is less than five, then the 4th decimal number shall remain unchanged.	
]]></text-property>
                                </text>
                            </cell>
                        </row>
                    </grid>
                    <text id="383">
                        <property name="fontWeight">bold</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[3. Account Details:]]></text-property>
                    </text>
                    <grid id="413">
                        <column id="414"/>
                        <column id="415"/>
                        <row id="416">
                            <cell id="417">
                                <text id="428">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Payments to Party A:]]></text-property>
                                </text>
                            </cell>
                            <cell id="418"/>
                        </row>
                        <row id="419">
                            <cell id="420">
                                <text id="429">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Account for payments:[To be provided]]]></text-property>
                                </text>
                            </cell>
                            <cell id="421"/>
                        </row>
                        <row id="422">
                            <cell id="423">
                                <text id="431">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Payments to Party B:]]></text-property>
                                </text>
                            </cell>
                            <cell id="424"/>
                        </row>
                        <row id="425">
                            <cell id="426">
                                <text id="430">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Account for payments:[To be provided]]]></text-property>
                                </text>
                            </cell>
                            <cell id="427"/>
                        </row>
                    </grid>
                    <text id="432">
                        <property name="fontWeight">bold</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[4. Additional Provisions:]]></text-property>
                    </text>
                    <text id="434">
                        <property name="fontWeight">bold</property>
                        <property name="color">#000000</property>
                        <property name="display">inline</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[Obligation to pay the absolute value of a negative Floating Amount.]]></text-property>
                    </text>
                    <text id="433">
                        <property name="display">inline</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[ Where the Floating Amount
payable by a party on a Settlement Date or Payment Date is a negative number, then the Floating
Amount payable by that party on that Settlement Date or Payment Date will be deemed to be zero,
and the other party will pay to that party the absolute value of that negative Floating Amount as
calculated, in addition to any amounts otherwise payable by that other party on that Settlement
Date or Payment Date.  For these purposes, such other party shall be the Floating Price Payer, the 
amount payable shall constitute a Floating Amount and rounding under Section 9.1 (Rounding in
Transactions) of the Commodity Definitions shall apply to the absolute value of the negative
Floating Amount as calculated provided that it shall be rounded to 4 decimal places, which amount 
shall be payable by such other party together with the Fixed Amount and any amounts otherwise
 payable by such other party on that Settlement Date or Payment Date.
]]></text-property>
                    </text>
                    <text id="435"/>
                    <text id="436">
                        <property name="fontWeight">bold</property>
                        <property name="display">inline</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[Additional Counterparty Charges]]></text-property>
                    </text>
                    <text id="437">
                        <property name="display">inline</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[In addition to, and without limitation on any other amounts payable in respect of this Transaction, Counterparty shall pay ]]></text-property>
                    </text>
                    <data id="650">
                        <property name="display">inline</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">CALCAGENT</property>
                                <text-property name="displayName">CALCAGENT</text-property>
                                <expression name="expression" type="javascript">dataSetRow["CALCAGENT"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">CALCAGENT</property>
                    </data>
                    <text id="651">
                        <property name="display">inline</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[, on each Settlement Date a fee in an amount equal to $0.10 per Unit of Commodity for that respective Calculation Period, in addition to any and all broker and, direct or indirect, collateral related costs arising from or relating to this Transaction, including, without limitation in respect of any hedging or other transactions that ]]></text-property>
                    </text>
                    <data id="647">
                        <property name="display">inline</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">CALCAGENT</property>
                                <text-property name="displayName">CALCAGENT</text-property>
                                <expression name="expression" type="javascript">dataSetRow["CALCAGENT"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">CALCAGENT</property>
                    </data>
                    <text id="643">
                        <property name="display">inline</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[ may enter into, in whole or in part, arising from or relating to this Transaction (all as determined by ]]></text-property>
                    </text>
                    <data id="648">
                        <property name="display">inline</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">CALCAGENT</property>
                                <text-property name="displayName">CALCAGENT</text-property>
                                <expression name="expression" type="javascript">dataSetRow["CALCAGENT"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">CALCAGENT</property>
                    </data>
                    <text id="649">
                        <property name="display">inline</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[ acting reasonably).]]></text-property>
                    </text>
                </cell>
            </row>
        </grid>
        <grid name="Signature" id="668">
            <column id="669"/>
            <row id="670">
                <cell id="671">
                    <text id="438">
                        <property name="fontWeight">bold</property>
                        <property name="textAlign">center</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[[SIGNATURES FOLLOW ON NEXT PAGE]]]></text-property>
                    </text>
                    <text id="439">
                        <property name="pageBreakBefore">always</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[Please confirm your agreement to be bound by the terms of the foregoing by executing a copy of this Confirmation and returning it to us by facsimile.]]></text-property>
                    </text>
                    <text id="459">
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[ ]]></text-property>
                    </text>
                    <grid id="440">
                        <property name="height">5.15625in</property>
                        <column id="441"/>
                        <column id="442"/>
                        <row id="443">
                            <cell id="444"/>
                            <cell id="445">
                                <data id="458">
                                    <property name="dataSet">Data Set 1</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">HOUSEFULLNAME</property>
                                            <text-property name="displayName">HOUSEFULLNAME</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["HOUSEFULLNAME"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">HOUSEFULLNAME</property>
                                </data>
                            </cell>
                        </row>
                        <row id="446">
                            <cell id="447"/>
                            <cell id="448">
                                <text id="460">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[By: _______________________]]></text-property>
                                </text>
                            </cell>
                        </row>
                        <row id="449">
                            <cell id="450"/>
                            <cell id="451">
                                <text id="461">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Name:]]></text-property>
                                </text>
                            </cell>
                        </row>
                        <row id="452">
                            <cell id="453"/>
                            <cell id="454">
                                <text id="462">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Title:]]></text-property>
                                </text>
                            </cell>
                        </row>
                        <row id="455">
                            <cell id="456"/>
                            <cell id="457">
                                <text id="463">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Date:]]></text-property>
                                </text>
                            </cell>
                        </row>
                        <row id="505">
                            <property name="height">0.5729166666666666in</property>
                            <cell id="506"/>
                            <cell id="507"/>
                        </row>
                        <row id="502">
                            <property name="height">0.3333333333333333in</property>
                            <cell id="503">
                                <text id="476">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Confirmed as of the date first above written: ]]></text-property>
                                </text>
                            </cell>
                            <cell id="504"/>
                        </row>
                        <row id="499">
                            <property name="height">0.3229166666666667in</property>
                            <cell id="500">
                                <data id="477">
                                    <property name="dataSet">Data Set 1</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">CPTY_DESCR</property>
                                            <text-property name="displayName">CPTY_DESCR</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["CPTY_DESCR"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">CPTY_DESCR</property>
                                </data>
                            </cell>
                            <cell id="501"/>
                        </row>
                        <row id="473">
                            <property name="height">0.3229166666666667in</property>
                            <cell id="474">
                                <text id="514">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[By: _______________________]]></text-property>
                                </text>
                            </cell>
                            <cell id="475"/>
                        </row>
                        <row id="511">
                            <property name="height">0.2708333333333333in</property>
                            <cell id="512">
                                <text id="520">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Name:]]></text-property>
                                </text>
                            </cell>
                            <cell id="513"/>
                        </row>
                        <row id="508">
                            <property name="height">0.34375in</property>
                            <cell id="509">
                                <text id="518">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Title:]]></text-property>
                                </text>
                            </cell>
                            <cell id="510"/>
                        </row>
                        <row id="515">
                            <property name="height">0.5729166666666666in</property>
                            <cell id="516">
                                <text id="519">
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Date:]]></text-property>
                                </text>
                            </cell>
                            <cell id="517"/>
                        </row>
                        <row id="464">
                            <property name="height">0.010416666666666666in</property>
                            <cell id="465"/>
                            <cell id="466"/>
                        </row>
                    </grid>
                </cell>
            </row>
        </grid>
        <grid name="Delivery Schedule" id="672">
            <column id="673"/>
            <row id="674">
                <cell id="675">
                    <text id="521">
                        <property name="fontWeight">bold</property>
                        <property name="textAlign">center</property>
                        <property name="pageBreakBefore">always</property>
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[Annex A]]></text-property>
                    </text>
                    <grid id="523">
                        <property name="dataSet">Data Set 1</property>
                        <column id="524">
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">row[""] == 'Y'</expression>
                                </structure>
                            </list-property>
                        </column>
                        <column id="525"/>
                        <row id="526">
                            <cell id="527">
                                <text id="522">
                                    <property name="display">inline</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Floating Price:]]></text-property>
                                </text>
                                <data id="607">
                                    <property name="display">inline</property>
                                    <property name="dataSet">Data Set 1</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">SIDE1</property>
                                            <text-property name="displayName">SIDE1</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["SIDE1"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">SIDE1</property>
                                </data>
                            </cell>
                            <cell id="528">
                                <property name="textAlign">right</property>
                                <text id="529">
                                    <property name="textAlign">left</property>
                                    <property name="display">inline</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Tkt Number:]]></text-property>
                                </text>
                                <data id="530">
                                    <property name="textAlign">left</property>
                                    <property name="display">inline</property>
                                    <property name="dataSet">Data Set 1</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">TNUM</property>
                                            <text-property name="displayName">TNUM</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["TNUM"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">TNUM</property>
                                </data>
                            </cell>
                        </row>
                    </grid>
                    <grid id="656">
                        <property name="height">1.1458333333333333in</property>
                        <property name="dataSet">Data Set 1</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">SHOWSCHEDULE</property>
                                <text-property name="displayName">SHOWSCHEDULE</text-property>
                                <expression name="expression" type="javascript">dataSetRow["SHOWSCHEDULE"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <column id="657"/>
                        <row id="658">
                            <property name="height">1.1458333333333333in</property>
                            <cell id="659">
                                <table id="531">
                                    <property name="dataSet">Data Set 2</property>
                                    <list-property name="visibility">
                                        <structure>
                                            <property name="format">all</property>
                                            <expression name="valueExpr" type="javascript">row._outer["SHOWSCHEDULE"] == 'N'</expression>
                                        </structure>
                                    </list-property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">TS_STARTTIME</property>
                                            <text-property name="displayName">TS_STARTTIME</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["TS_STARTTIME"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                        <structure>
                                            <property name="name">TS_ENDTIME</property>
                                            <text-property name="displayName">TS_ENDTIME</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["TS_ENDTIME"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                        <structure>
                                            <property name="name">DELDAYS</property>
                                            <text-property name="displayName">DELDAYS</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["DELDAYS"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                        <structure>
                                            <property name="name">TIMES_</property>
                                            <text-property name="displayName">TIMES_</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["TIMES_"] + "-" + dataSetRow["TIMEE_"]</expression>
                                            <property name="dataType">string</property>
                                            <property name="allowExport">true</property>
                                        </structure>
                                        <structure>
                                            <property name="name">TIMEE_</property>
                                            <text-property name="displayName">TIMEE_</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["TIMEE_"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                        <structure>
                                            <property name="name">QTY</property>
                                            <text-property name="displayName">QTY</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["QTY"]</expression>
                                            <property name="dataType">decimal</property>
                                        </structure>
                                        <structure>
                                            <property name="name">AMT_PERIOD</property>
                                            <text-property name="displayName">AMT_PERIOD</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["AMT_PERIOD"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                        <structure>
                                            <property name="name">AMT_UNIT</property>
                                            <text-property name="displayName">AMT_UNIT</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["AMT_UNIT"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                        <structure>
                                            <property name="name">FLOAT_MULT</property>
                                            <text-property name="displayName">FLOAT_MULT</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["FLOAT_MULT"]</expression>
                                            <property name="dataType">decimal</property>
                                        </structure>
                                        <structure>
                                            <property name="name">PRICE_TRD</property>
                                            <text-property name="displayName">PRICE_TRD</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["PRICE_TRD"]</expression>
                                            <property name="dataType">decimal</property>
                                        </structure>
                                    </list-property>
                                    <column id="560"/>
                                    <column id="561"/>
                                    <column id="562"/>
                                    <column id="563"/>
                                    <column id="564"/>
                                    <column id="565"/>
                                    <column id="566"/>
                                    <column id="567"/>
                                    <column id="568"/>
                                    <header>
                                        <row id="532">
                                            <cell id="533">
                                                <property name="textAlign">left</property>
                                                <text id="227">
                                                    <property name="fontSize">10pt</property>
                                                    <property name="textAlign">left</property>
                                                    <property name="contentType">auto</property>
                                                    <text-property name="content"><![CDATA[Start Date]]></text-property>
                                                </text>
                                            </cell>
                                            <cell id="534">
                                                <property name="textAlign">left</property>
                                                <text id="228">
                                                    <property name="fontSize">10pt</property>
                                                    <property name="textAlign">left</property>
                                                    <property name="contentType">auto</property>
                                                    <text-property name="content"><![CDATA[End Date]]></text-property>
                                                </text>
                                            </cell>
                                            <cell id="535">
                                                <property name="textAlign">left</property>
                                                <text id="229">
                                                    <property name="fontSize">10pt</property>
                                                    <property name="textAlign">left</property>
                                                    <property name="contentType">auto</property>
                                                    <text-property name="content"><![CDATA[Days]]></text-property>
                                                </text>
                                            </cell>
                                            <cell id="536">
                                                <property name="textAlign">left</property>
                                                <text id="230">
                                                    <property name="fontSize">10pt</property>
                                                    <property name="textAlign">left</property>
                                                    <property name="contentType">auto</property>
                                                    <text-property name="content"><![CDATA[Hours Ending]]></text-property>
                                                </text>
                                            </cell>
                                            <cell id="537">
                                                <property name="textAlign">left</property>
                                                <text id="231">
                                                    <property name="fontSize">10pt</property>
                                                    <property name="textAlign">left</property>
                                                    <property name="contentType">auto</property>
                                                    <text-property name="content"><![CDATA[Amt/Per]]></text-property>
                                                </text>
                                            </cell>
                                            <cell id="538">
                                                <property name="textAlign">left</property>
                                                <text id="232">
                                                    <property name="fontSize">10pt</property>
                                                    <property name="textAlign">left</property>
                                                    <property name="contentType">auto</property>
                                                    <text-property name="content"><![CDATA[Unit]]></text-property>
                                                </text>
                                            </cell>
                                            <cell id="539">
                                                <property name="textAlign">left</property>
                                                <text id="233">
                                                    <property name="fontSize">10pt</property>
                                                    <property name="textAlign">left</property>
                                                    <property name="contentType">auto</property>
                                                    <text-property name="content"><![CDATA[Period]]></text-property>
                                                </text>
                                            </cell>
                                            <cell id="540">
                                                <property name="textAlign">left</property>
                                                <text id="234">
                                                    <property name="fontSize">10pt</property>
                                                    <property name="textAlign">left</property>
                                                    <property name="contentType">auto</property>
                                                    <text-property name="content"><![CDATA[$/Unit]]></text-property>
                                                </text>
                                            </cell>
                                            <cell id="541">
                                                <property name="textAlign">left</property>
                                                <text id="235">
                                                    <property name="fontSize">10pt</property>
                                                    <property name="textAlign">left</property>
                                                    <property name="contentType">auto</property>
                                                    <text-property name="content"><![CDATA[Heat Rate]]></text-property>
                                                </text>
                                            </cell>
                                        </row>
                                    </header>
                                    <detail>
                                        <row id="542">
                                            <property name="height">24pt</property>
                                            <cell id="543">
                                                <property name="textAlign">left</property>
                                                <data id="776">
                                                    <property name="resultSetColumn">TS_STARTTIME</property>
                                                </data>
                                            </cell>
                                            <cell id="571">
                                                <property name="textAlign">left</property>
                                                <data id="775">
                                                    <property name="resultSetColumn">TS_ENDTIME</property>
                                                </data>
                                            </cell>
                                            <cell id="573">
                                                <property name="textAlign">left</property>
                                                <data id="574">
                                                    <property name="fontSize">10pt</property>
                                                    <property name="resultSetColumn">DELDAYS</property>
                                                </data>
                                            </cell>
                                            <cell id="544">
                                                <property name="textAlign">left</property>
                                                <data id="575">
                                                    <property name="fontSize">10pt</property>
                                                    <property name="resultSetColumn">TIMES_</property>
                                                </data>
                                            </cell>
                                            <cell id="545">
                                                <property name="textAlign">left</property>
                                                <data id="576">
                                                    <property name="fontSize">10pt</property>
                                                    <property name="resultSetColumn">QTY</property>
                                                </data>
                                            </cell>
                                            <cell id="546">
                                                <property name="textAlign">left</property>
                                                <data id="577">
                                                    <property name="fontSize">10pt</property>
                                                    <property name="resultSetColumn">AMT_UNIT</property>
                                                </data>
                                            </cell>
                                            <cell id="547">
                                                <property name="textAlign">left</property>
                                                <data id="578">
                                                    <property name="fontSize">10pt</property>
                                                    <property name="resultSetColumn">AMT_PERIOD</property>
                                                </data>
                                            </cell>
                                            <cell id="548">
                                                <property name="textAlign">left</property>
                                                <data id="595">
                                                    <property name="resultSetColumn">PRICE_TRD</property>
                                                </data>
                                            </cell>
                                            <cell id="549">
                                                <property name="textAlign">left</property>
                                                <data id="594">
                                                    <property name="resultSetColumn">FLOAT_MULT</property>
                                                </data>
                                            </cell>
                                        </row>
                                    </detail>
                                </table>
                            </cell>
                        </row>
                    </grid>
                </cell>
            </row>
        </grid>
        <text id="608">
            <property name="display">inline</property>
            <property name="contentType">auto</property>
            <text-property name="content"><![CDATA[The Specified Price is the official settlement price published by ]]></text-property>
        </text>
        <data id="615">
            <property name="display">inline</property>
            <property name="dataSet">Data Set 1</property>
            <list-property name="boundDataColumns">
                <structure>
                    <property name="name">MKT1_</property>
                    <text-property name="displayName">MKT1_</text-property>
                    <expression name="expression" type="javascript">dataSetRow["MKT1_"] ? dataSetRow["MKT1"].trim() : dataSetRow["MKT1"]</expression>
                    <property name="dataType">string</property>
                    <property name="allowExport">true</property>
                </structure>
            </list-property>
            <property name="resultSetColumn">MKT1_</property>
        </data>
        <text id="611">
            <property name="display">inline</property>
            <property name="contentType">auto</property>
            <text-property name="content"><![CDATA[ per Unit of Commodity, published as the]]></text-property>
        </text>
        <data id="610">
            <property name="display">inline</property>
            <property name="dataSet">Data Set 1</property>
            <list-property name="boundDataColumns">
                <structure>
                    <property name="name">FIX_METHD</property>
                    <text-property name="displayName">FIX_METHD</text-property>
                    <expression name="expression" type="javascript">dataSetRow["FIX_METHD"] ? dataSetRow["FIX_METHD"].trim() : dataSetRow["FIX_METHD"]</expression>
                    <property name="dataType">string</property>
                    <property name="allowExport">true</property>
                </structure>
            </list-property>
            <property name="resultSetColumn">FIX_METHD</property>
        </data>
        <text id="609">
            <property name="display">inline</property>
            <property name="contentType">auto</property>
            <text-property name="content"><![CDATA[ for the ]]></text-property>
        </text>
        <data id="614">
            <property name="display">inline</property>
            <property name="dataSet">Data Set 1</property>
            <list-property name="boundDataColumns">
                <structure>
                    <property name="name">CDYDESC1</property>
                    <text-property name="displayName">CDYDESC1</text-property>
                    <expression name="expression" type="javascript">dataSetRow["CDYDESC1"]</expression>
                    <property name="dataType">string</property>
                </structure>
            </list-property>
            <property name="resultSetColumn">CDYDESC1</property>
        </data>
        <text id="613">
            <property name="display">inline</property>
            <property name="contentType">auto</property>
            <text-property name="content"><![CDATA[for the applicable Hours Ending (as set forth above) during such Pricing Date.]]></text-property>
        </text>
    </body>
</report>
